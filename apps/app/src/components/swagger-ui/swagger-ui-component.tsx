"use client"

import { useEffect, useRef } from "react"
import { NextApiRequest } from "next"
import Swagger<PERSON> from "swagger-ui-react"

import { env } from "@/lib/env"
import { logger } from "@proamlink/lib"

import "swagger-ui-react/swagger-ui.css"

interface SwaggerUIComponentProps {
  openApiUrl: string
}

/**
 * Composant client pour afficher Swagger UI
 * 
 * Ce composant utilise swagger-ui-react pour afficher la documentation
 * interactive de l'API ProAmLink. Il est séparé en composant client
 * pour éviter les problèmes de SSR avec Swagger UI.
 */
export default function SwaggerUIComponent({ openApiUrl }: SwaggerUIComponentProps) {
  const containerRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    // Ajout de styles personnalisés pour améliorer l'apparence
    const style = document.createElement("style")
    style.textContent = `
      /* Personnalisation de Swagger UI pour ProAmLink */
      .swagger-ui .topbar {
        background-color: #1f2937;
        border-bottom: 1px solid #374151;
      }
      
      .swagger-ui .topbar .download-url-wrapper {
        display: none; /* Masquer le champ URL */
      }
      
      .swagger-ui .info {
        margin: 20px 0;
      }
      
      .swagger-ui .info .title {
        color: #1f2937;
        font-size: 2rem;
        font-weight: bold;
      }
      
      .swagger-ui .info .description {
        color: #4b5563;
        line-height: 1.6;
      }
      
      .swagger-ui .scheme-container {
        background: #f9fafb;
        border: 1px solid #e5e7eb;
        border-radius: 8px;
        padding: 15px;
        margin: 20px 0;
      }
      
      .swagger-ui .opblock.opblock-get {
        border-color: #10b981;
        background: rgba(16, 185, 129, 0.1);
      }
      
      .swagger-ui .opblock.opblock-post {
        border-color: #3b82f6;
        background: rgba(59, 130, 246, 0.1);
      }
      
      .swagger-ui .opblock.opblock-put {
        border-color: #f59e0b;
        background: rgba(245, 158, 11, 0.1);
      }
      
      .swagger-ui .opblock.opblock-delete {
        border-color: #ef4444;
        background: rgba(239, 68, 68, 0.1);
      }
      
      /* Amélioration de la lisibilité */
      .swagger-ui .opblock .opblock-summary {
        font-weight: 600;
      }
      
      .swagger-ui .parameter__name {
        font-weight: 600;
        color: #1f2937;
      }
      
      .swagger-ui .response-col_status {
        font-weight: 600;
      }
      
      /* Responsive design */
      @media (max-width: 768px) {
        .swagger-ui .info .title {
          font-size: 1.5rem;
        }
        
        .swagger-ui .opblock {
          margin: 10px 0;
        }
      }
    `
    document.head.appendChild(style)

    // Nettoyage lors du démontage du composant
    return () => {
      if (document.head.contains(style)) {
        document.head.removeChild(style)
      }
    }
  }, [])

  /**
   * Configuration personnalisée pour Swagger UI
   */
  const swaggerConfig = {
    // URL de la spécification OpenAPI
    url: openApiUrl,

    // Configuration de l'interface
    docExpansion: "list" as const, // Expansion des sections par défaut
    defaultModelsExpandDepth: 2, // Profondeur d'expansion des modèles
    defaultModelExpandDepth: 2,
    displayRequestDuration: true, // Afficher la durée des requêtes
    filter: true, // Activer le filtre de recherche
    showExtensions: true, // Afficher les extensions OpenAPI
    showCommonExtensions: true,

    // Configuration des requêtes
    supportedSubmitMethods: ["get", "post", "put", "delete", "patch"] as const,
    tryItOutEnabled: true, // Activer le bouton "Try it out"

    // Configuration de l'authentification
    persistAuthorization: true, // Persister l'authentification entre les requêtes

    // Personnalisation de l'interface
    layout: "BaseLayout",
    deepLinking: true, // Activer les liens profonds
    displayOperationId: false, // Masquer les IDs d'opération

    // Messages personnalisés en français
    validatorUrl: null, // Désactiver la validation externe

    // Configuration des requêtes
    requestInterceptor: (request: NextApiRequest) => {
      // Ajouter des headers personnalisés si nécessaire
      request.headers["Accept"] = "application/json"
      request.headers["Content-Type"] = "application/json"

      // Log des requêtes pour le debugging (en développement uniquement)
      if (env.ENV === "development") {
        logger.log("Swagger UI Request:", request)
      }

      return request
    },

    // Configuration des réponses
    responseInterceptor: (response: string) => {
      // Log des réponses pour le debugging (en développement uniquement)
      if (env.ENV === "development") {
        logger.log("Swagger UI Response:", response)
      }

      return response
    },
  }

  return (
    <div ref={containerRef} className="swagger-ui-container">
      <SwaggerUI {...swaggerConfig} />
    </div>
  )
}
