import { z } from "zod"

/**
 * Schémas Zod pour les endpoints publics de l'API mobile
 * 
 * Ces schémas définissent la validation des données pour les endpoints
 * accessibles sans authentification depuis les applications mobiles.
 */

// ============================================================================
// Health Check Schemas
// ============================================================================

export const healthCheckResponseSchema = () =>
  z.object({
    status: z.literal("ok").describe("État de l'API"),
    timestamp: z.string().describe("Horodatage de la vérification"),
    version: z.string().describe("Version de l'API mobile"),
    environment: z.string().describe("Environnement d'exécution"),
    services: z.object({
      database: z.enum(["connected", "disconnected"]).describe("État de la base de données"),
      cache: z.enum(["connected", "disconnected"]).describe("État du cache Redis"),
    }).describe("État des services externes"),
  })

// ============================================================================
// Authentication Schemas
// ============================================================================

export const mobileLoginSchema = () =>
  z.object({
    email: z.string().email().describe("Adresse email de l'utilisateur"),
    password: z.string().min(1).describe("Mot de passe de l'utilisateur"),
    deviceId: z.string().optional().describe("Identifiant unique de l'appareil mobile"),
    deviceName: z.string().optional().describe("Nom de l'appareil (ex: iPhone 15, Samsung Galaxy S24)"),
  })

export const mobileLoginResponseSchema = () =>
  z.object({
    success: z.boolean().describe("Indique si la connexion a réussi"),
    token: z.string().optional().describe("Token d'authentification JWT"),
    user: z.object({
      id: z.string().describe("Identifiant unique de l'utilisateur"),
      email: z.string().describe("Adresse email de l'utilisateur"),
      username: z.string().nullable().describe("Nom d'utilisateur"),
      profilePicture: z.string().nullable().describe("URL de la photo de profil"),
      emailVerified: z.boolean().describe("Indique si l'email est vérifié"),
    }).optional().describe("Informations de l'utilisateur connecté"),
    message: z.string().describe("Message de statut"),
  })

export const mobileRegisterSchema = () =>
  z.object({
    email: z.string().email().describe("Adresse email de l'utilisateur"),
    password: z.string().min(8).describe("Mot de passe (minimum 8 caractères)"),
    username: z.string().min(3).max(30).describe("Nom d'utilisateur (3-30 caractères)"),
    deviceId: z.string().optional().describe("Identifiant unique de l'appareil mobile"),
    deviceName: z.string().optional().describe("Nom de l'appareil"),
    acceptTerms: z.boolean().describe("Acceptation des conditions d'utilisation"),
  })

export const mobileRegisterResponseSchema = () =>
  z.object({
    success: z.boolean().describe("Indique si l'inscription a réussi"),
    user: z.object({
      id: z.string().describe("Identifiant unique de l'utilisateur"),
      email: z.string().describe("Adresse email de l'utilisateur"),
      username: z.string().describe("Nom d'utilisateur"),
    }).optional().describe("Informations de l'utilisateur créé"),
    message: z.string().describe("Message de statut"),
    requiresEmailVerification: z.boolean().describe("Indique si une vérification email est requise"),
  })

// ============================================================================
// App Info Schemas
// ============================================================================

export const appInfoResponseSchema = () =>
  z.object({
    appName: z.string().describe("Nom de l'application"),
    version: z.string().describe("Version de l'API"),
    supportedVersions: z.object({
      minimum: z.string().describe("Version minimum supportée de l'app mobile"),
      recommended: z.string().describe("Version recommandée de l'app mobile"),
    }).describe("Versions supportées de l'application mobile"),
    features: z.object({
      registration: z.boolean().describe("Inscription activée"),
      socialLogin: z.boolean().describe("Connexion via réseaux sociaux activée"),
      twoFactorAuth: z.boolean().describe("Authentification à deux facteurs activée"),
      fileUpload: z.boolean().describe("Upload de fichiers activé"),
    }).describe("Fonctionnalités disponibles"),
    maintenance: z.object({
      active: z.boolean().describe("Mode maintenance actif"),
      message: z.string().optional().describe("Message de maintenance"),
      estimatedEnd: z.string().optional().describe("Fin estimée de la maintenance"),
    }).describe("Informations de maintenance"),
  })

// ============================================================================
// Password Reset Schemas
// ============================================================================

export const forgotPasswordMobileSchema = () =>
  z.object({
    email: z.string().email().describe("Adresse email pour la réinitialisation"),
  })

export const forgotPasswordMobileResponseSchema = () =>
  z.object({
    success: z.boolean().describe("Indique si la demande a été traitée"),
    message: z.string().describe("Message de confirmation"),
  })

export const resetPasswordMobileSchema = () =>
  z.object({
    token: z.string().min(1).describe("Token de réinitialisation reçu par email"),
    newPassword: z.string().min(8).describe("Nouveau mot de passe (minimum 8 caractères)"),
  })

export const resetPasswordMobileResponseSchema = () =>
  z.object({
    success: z.boolean().describe("Indique si la réinitialisation a réussi"),
    message: z.string().describe("Message de statut"),
  })
