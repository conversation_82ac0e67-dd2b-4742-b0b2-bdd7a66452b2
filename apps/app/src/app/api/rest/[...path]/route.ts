import { NextRequest, NextResponse } from "next/server"

import { env } from "@/lib/env"
import { logger } from "@proamlink/lib"

/**
 * Handler REST simple pour les requêtes OpenAPI
 *
 * Cette route capture toutes les requêtes vers /api/rest/* et retourne
 * des réponses appropriées pour tester l'architecture.
 *
 * Exemples d'URLs capturées :
 * - GET /api/rest/health
 * - POST /api/rest/auth/register
 * - GET /api/rest/me/account
 * - etc.
 */

/**
 * Handler simple pour tester les endpoints REST
 */
const handler = async (req: NextRequest) => {
  try {
    const url = new URL(req.url)
    const path = url.pathname

    logger.log(`🔄 REST Request: ${req.method} ${path}`)

    // Endpoint de santé
    if (path.includes("/health")) {
      return NextResponse.json({
        status: "ok",
        timestamp: new Date().toISOString(),
        version: "1.0.0",
        environment: env.ENV || "development",
        services: {
          database: "connected",
          redis: "connected",
        },
      })
    }

    // Pour les autres endpoints, retourner une réponse de développement
    return NextResponse.json(
      {
        message: "REST endpoint under development",
        method: req.method,
        path: path,
        timestamp: new Date().toISOString(),
        note: "This endpoint will be implemented with full tRPC-OpenAPI integration",
      },
      { status: 501 }
    )
  } catch (error) {
    console.error("❌ REST Handler Error:", error)
    return NextResponse.json(
      {
        error: "Internal server error",
        message: "An error occurred while processing the request",
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    )
  }
}

// Export des handlers pour tous les verbes HTTP
export const GET = handler
export const POST = handler
export const PUT = handler
export const DELETE = handler
export const PATCH = handler
export const OPTIONS = handler
