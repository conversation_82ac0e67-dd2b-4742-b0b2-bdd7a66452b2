import { createOpenApiFetchHandler } from "trpc-to-openapi"

import { appRouter } from "@/api/_app"
import { createContext } from "@/lib/trpc/context"

/**
 * Handler REST pour les requêtes OpenAPI via tRPC
 *
 * Cette route capture toutes les requêtes vers /api/rest/* et les route
 * automatiquement vers les procédures tRPC correspondantes en utilisant
 * les métadonnées OpenAPI définies dans chaque procédure.
 *
 * Fonctionnalités :
 * - Routage automatique des requêtes REST vers les procédures tRPC
 * - Validation automatique des paramètres d'entrée
 * - Sérialisation automatique des réponses
 * - Gestion d'erreurs cohérente
 * - Support de l'authentification Bearer
 *
 * Exemples d'URLs supportées :
 * - GET /api/rest/health → system.health
 * - POST /api/rest/auth/register → auth.register
 * - GET /api/rest/me/account → me.getAccount
 * - etc.
 */

/**
 * Handler principal utilisant trpc-to-openapi pour router les requêtes REST
 * vers les procédures tRPC correspondantes
 */
const handler = (req: Request) =>
  createOpenApiFetchHandler({
    endpoint: "/api/rest",
    req,
    router: appRouter,
    createContext: (opts) => createContext(opts),
    onError: ({ error, type, path, input, req }) => {
      console.error(`❌ tRPC-OpenAPI Error [${type}] on ${path}:`, error)

      // Log additional context for debugging in development
      if (typeof process !== "undefined" && process.env?.NODE_ENV === "development") {
        console.error("Request details:", {
          method: req.method,
          url: req.url,
          headers: Object.fromEntries(req.headers.entries()),
          input,
        })
      }
    },
    responseMeta: ({ type, errors }) => {
      // Add custom headers for all responses
      const headers: Record<string, string> = {
        "Access-Control-Allow-Origin": "*",
        "Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, PATCH, OPTIONS",
        "Access-Control-Allow-Headers": "Content-Type, Authorization",
      }

      // Add cache headers for successful GET requests
      if (type === "query" && errors.length === 0) {
        headers["Cache-Control"] = "public, max-age=60"
      }

      return {
        status: errors.length > 0 ? 500 : 200,
        headers,
      }
    },
  })

// Export des handlers pour tous les verbes HTTP
export const GET = handler
export const POST = handler
export const PUT = handler
export const DELETE = handler
export const PATCH = handler
export const OPTIONS = handler
