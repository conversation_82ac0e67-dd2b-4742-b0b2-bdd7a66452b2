import { NextResponse } from "next/server"
import { generateOpenApiDocument } from "trpc-to-openapi"

import { appRouter } from "@/api/_app"
import { env } from "@/lib/env"

/**
 * Endpoint pour générer la spécification OpenAPI
 *
 * Ce endpoint génère automatiquement la documentation OpenAPI 3.0
 * à partir des procédures tRPC annotées avec les métadonnées OpenAPI.
 *
 * La spécification générée peut être utilisée pour :
 * - Générer des SDKs clients (Flutter, React Native, etc.)
 * - Alimenter Swagger UI pour la documentation interactive
 * - Valider les contrats d'API
 * - Générer des tests automatiques
 */

/**
 * Configuration de la documentation OpenAPI
 */
const openApiDocument = generateOpenApiDocument(appRouter, {
  title: "API ProAmLink",
  description: `
    API REST pour l'application ProAmLink - Plateforme de mise en relation professionnelle.

    Cette API fournit des endpoints pour :
    - Gestion des utilisateurs et authentification
    - Profils professionnels et networking
    - Messagerie et communications
    - Gestion des fichiers et médias

    ## Authentification

    La plupart des endpoints nécessitent une authentification via token Bearer.
    Utilisez l'endpoint de connexion pour obtenir un token d'accès.

    ## Codes d'erreur

    L'API utilise les codes de statut HTTP standards :
    - 200: Succès
    - 400: Erreur de validation des données
    - 401: Non authentifié
    - 403: Non autorisé
    - 404: Ressource non trouvée
    - 500: Erreur serveur interne

    ## Pagination

    Les endpoints qui retournent des listes supportent la pagination via les paramètres :
    - \`limit\`: Nombre d'éléments par page (défaut: 20, max: 100)
    - \`offset\`: Décalage pour la pagination (défaut: 0)

    ## Versioning

    Cette API suit le versioning sémantique. Les changements breaking sont accompagnés
    d'une nouvelle version majeure.
  `,
  version: "1.0.0",
  baseUrl: env.NEXT_PUBLIC_BASE_URL || "http://localhost:3000",
  docsUrl: `${env.NEXT_PUBLIC_BASE_URL || "http://localhost:3000"}/api/docs`,
  tags: ["Santé", "Utilisateurs", "Authentification", "Profils", "Fichiers"],
  securitySchemes: {
    bearerAuth: {
      type: "http",
      scheme: "bearer",
      bearerFormat: "JWT",
      description: "Token d'authentification JWT obtenu via l'endpoint de connexion",
    },
  },
})

/**
 * Handler GET pour récupérer la spécification OpenAPI
 *
 * @param request - Requête HTTP Next.js
 * @returns Spécification OpenAPI au format JSON
 */
export async function GET(): Promise<NextResponse> {
  try {
    // Headers pour permettre l'accès CORS et indiquer le type de contenu
    const headers = {
      "Content-Type": "application/json",
      "Access-Control-Allow-Origin": "*",
      "Access-Control-Allow-Methods": "GET",
      "Access-Control-Allow-Headers": "Content-Type",
      "Cache-Control": "public, max-age=3600", // Cache pendant 1 heure
    }

    // Retourner la spécification OpenAPI
    return NextResponse.json(openApiDocument, {
      status: 200,
      headers,
    })
  } catch (error) {
    console.error("Erreur lors de la génération de la spécification OpenAPI:", error)

    return NextResponse.json(
      {
        error: {
          code: "OPENAPI_GENERATION_ERROR",
          message: "Erreur lors de la génération de la spécification OpenAPI",
          details: error instanceof Error ? error.message : "Erreur inconnue",
        },
        timestamp: new Date().toISOString(),
      },
      {
        status: 500,
        headers: {
          "Content-Type": "application/json",
        },
      }
    )
  }
}

/**
 * Handler OPTIONS pour supporter les requêtes CORS preflight
 */
export async function OPTIONS(): Promise<NextResponse> {
  return NextResponse.json(
    {},
    {
      status: 200,
      headers: {
        "Access-Control-Allow-Origin": "*",
        "Access-Control-Allow-Methods": "GET, OPTIONS",
        "Access-Control-Allow-Headers": "Content-Type",
      },
    }
  )
}
