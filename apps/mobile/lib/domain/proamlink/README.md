# proamlink_api (EXPERIMENTAL)

    API REST pour l'application ProAmLink - Plateforme de mise en relation professionnelle.

    Cette API fournit des endpoints pour :
    - Gestion des utilisateurs et authentification
    - Profils professionnels et networking
    - Messagerie et communications
    - Gestion des fichiers et médias

    ## Authentification

    La plupart des endpoints nécessitent une authentification via token Bearer.
    Utilisez l'endpoint de connexion pour obtenir un token d'accès.

    ## Codes d'erreur

    L'API utilise les codes de statut HTTP standards :
    - 200: Succès
    - 400: Erreur de validation des données
    - 401: Non authentifié
    - 403: Non autorisé
    - 404: Ressource non trouvée
    - 500: Erreur serveur interne

    ## Pagination

    Les endpoints qui retournent des listes supportent la pagination via les paramètres :
    - `limit`: Nombre d'éléments par page (défaut: 20, max: 100)
    - `offset`: Décalage pour la pagination (défaut: 0)

    ## Versioning

    Cette API suit le versioning sémantique. Les changements breaking sont accompagnés
    d'une nouvelle version majeure.
  

This Dart package is automatically generated by the [OpenAPI Generator](https://openapi-generator.tech) project:

- API version: 1.0.0
- Generator version: 7.14.0
- Build package: org.openapitools.codegen.languages.DartDioClientCodegen

## Requirements

* Dart 2.15.0+ or Flutter 2.8.0+
* Dio 5.0.0+ (https://pub.dev/packages/dio)

## Installation & Usage

### pub.dev
To use the package from [pub.dev](https://pub.dev), please include the following in pubspec.yaml
```yaml
dependencies:
  proamlink_api: 1.0.0
```

### Github
If this Dart package is published to Github, please include the following in pubspec.yaml
```yaml
dependencies:
  proamlink_api:
    git:
      url: https://github.com/GIT_USER_ID/GIT_REPO_ID.git
      #ref: main
```

### Local development
To use the package from your local drive, please include the following in pubspec.yaml
```yaml
dependencies:
  proamlink_api:
    path: /path/to/proamlink_api
```

## Getting Started

Please follow the [installation procedure](#installation--usage) and then run the following:

```dart
import 'package:proamlink_api/proamlink_api.dart';


final api = ProamlinkApi().getAuthentificationApi();
final AuthRegisterRequest authRegisterRequest = ; // AuthRegisterRequest | 

try {
    final response = await api.authRegister(authRegisterRequest);
    print(response);
} catch on DioException (e) {
    print("Exception when calling AuthentificationApi->authRegister: $e\n");
}

```

## Documentation for API Endpoints

All URIs are relative to *http://localhost:3000*

Class | Method | HTTP request | Description
------------ | ------------- | ------------- | -------------
[*AuthentificationApi*](doc/AuthentificationApi.md) | [**authRegister**](doc/AuthentificationApi.md#authregister) | **POST** /api/rest/auth/register | Inscription d&#39;un nouvel utilisateur
[*AuthentificationApi*](doc/AuthentificationApi.md) | [**meForgotPassword**](doc/AuthentificationApi.md#meforgotpassword) | **POST** /api/rest/me/forgot-password | Demande de réinitialisation de mot de passe
[*AuthentificationApi*](doc/AuthentificationApi.md) | [**meResetPassword**](doc/AuthentificationApi.md#meresetpassword) | **POST** /api/rest/me/reset-password | Réinitialisation du mot de passe
[*Authentification2FAApi*](doc/Authentification2FAApi.md) | [**authDesactivateTotp**](doc/Authentification2FAApi.md#authdesactivatetotp) | **POST** /api/rest/auth/2fa/deactivate | Désactivation de l&#39;authentification 2FA
[*Authentification2FAApi*](doc/Authentification2FAApi.md) | [**authGenerateTotpSecret**](doc/Authentification2FAApi.md#authgeneratetotpsecret) | **POST** /api/rest/auth/2fa/generate-secret | Génération d&#39;un secret TOTP
[*Authentification2FAApi*](doc/Authentification2FAApi.md) | [**authRecover2FA**](doc/Authentification2FAApi.md#authrecover2fa) | **POST** /api/rest/auth/2fa/recover | Récupération d&#39;accès 2FA
[*Authentification2FAApi*](doc/Authentification2FAApi.md) | [**authVerifyTotp**](doc/Authentification2FAApi.md#authverifytotp) | **POST** /api/rest/auth/2fa/verify | Vérification du code TOTP
[*ProfilUtilisateurApi*](doc/ProfilUtilisateurApi.md) | [**meDeleteAccount**](doc/ProfilUtilisateurApi.md#medeleteaccount) | **DELETE** /api/rest/me/account | Suppression du compte utilisateur
[*ProfilUtilisateurApi*](doc/ProfilUtilisateurApi.md) | [**meGetAccount**](doc/ProfilUtilisateurApi.md#megetaccount) | **GET** /api/rest/me/account | Récupération du compte utilisateur
[*ProfilUtilisateurApi*](doc/ProfilUtilisateurApi.md) | [**meUpdateUser**](doc/ProfilUtilisateurApi.md#meupdateuser) | **PUT** /api/rest/me/profile | Mise à jour du profil utilisateur
[*SessionsApi*](doc/SessionsApi.md) | [**meDeleteSession**](doc/SessionsApi.md#medeletesession) | **DELETE** /api/rest/me/sessions/{id} | Suppression d&#39;une session
[*SessionsApi*](doc/SessionsApi.md) | [**meGetActiveSessions**](doc/SessionsApi.md#megetactivesessions) | **GET** /api/rest/me/sessions | Récupération des sessions actives
[*SystmeApi*](doc/SystmeApi.md) | [**systemHealth**](doc/SystmeApi.md#systemhealth) | **GET** /api/rest/health | Vérification de l&#39;état de l&#39;API
[*UploadApi*](doc/UploadApi.md) | [**uploadPresignedUrl**](doc/UploadApi.md#uploadpresignedurl) | **POST** /api/rest/upload/presigned-url | Génération d&#39;une URL pré-signée
[*VrificationEmailApi*](doc/VrificationEmailApi.md) | [**meVerifyEmail**](doc/VrificationEmailApi.md#meverifyemail) | **POST** /api/rest/me/verify-email | Vérification de l&#39;adresse email


## Documentation For Models

 - [AuthGenerateTotpSecret200Response](doc/AuthGenerateTotpSecret200Response.md)
 - [AuthRecover2FARequest](doc/AuthRecover2FARequest.md)
 - [AuthRegister200Response](doc/AuthRegister200Response.md)
 - [AuthRegister200ResponseUser](doc/AuthRegister200ResponseUser.md)
 - [AuthRegister200ResponseUserProfilePicture](doc/AuthRegister200ResponseUserProfilePicture.md)
 - [AuthRegisterRequest](doc/AuthRegisterRequest.md)
 - [AuthVerifyTotp200Response](doc/AuthVerifyTotp200Response.md)
 - [AuthVerifyTotpRequest](doc/AuthVerifyTotpRequest.md)
 - [ErrorBADREQUEST](doc/ErrorBADREQUEST.md)
 - [ErrorBADREQUESTIssuesInner](doc/ErrorBADREQUESTIssuesInner.md)
 - [ErrorFORBIDDEN](doc/ErrorFORBIDDEN.md)
 - [ErrorINTERNALSERVERERROR](doc/ErrorINTERNALSERVERERROR.md)
 - [ErrorNOTFOUND](doc/ErrorNOTFOUND.md)
 - [ErrorUNAUTHORIZED](doc/ErrorUNAUTHORIZED.md)
 - [MeDeleteAccount200Response](doc/MeDeleteAccount200Response.md)
 - [MeDeleteSession200Response](doc/MeDeleteSession200Response.md)
 - [MeForgotPassword200Response](doc/MeForgotPassword200Response.md)
 - [MeForgotPasswordRequest](doc/MeForgotPasswordRequest.md)
 - [MeGetActiveSessions200Response](doc/MeGetActiveSessions200Response.md)
 - [MeGetActiveSessions200ResponseDataInner](doc/MeGetActiveSessions200ResponseDataInner.md)
 - [MeGetActiveSessions200ResponseMeta](doc/MeGetActiveSessions200ResponseMeta.md)
 - [MeResetPasswordRequest](doc/MeResetPasswordRequest.md)
 - [MeUpdateUserRequest](doc/MeUpdateUserRequest.md)
 - [MeUpdateUserRequestUsername](doc/MeUpdateUserRequestUsername.md)
 - [SystemHealth200Response](doc/SystemHealth200Response.md)
 - [SystemHealth200ResponseServices](doc/SystemHealth200ResponseServices.md)
 - [UploadPresignedUrl200Response](doc/UploadPresignedUrl200Response.md)
 - [UploadPresignedUrlRequest](doc/UploadPresignedUrlRequest.md)


## Documentation For Authorization


Authentication schemes defined for the API:
### bearerAuth

- **Type**: HTTP Bearer Token authentication (JWT)


## Author



