//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'auth_recover2_fa_request.g.dart';

/// AuthRecover2FARequest
///
/// Properties:
/// * [email] 
/// * [mnemonic] 
@BuiltValue()
abstract class AuthRecover2FARequest implements Built<AuthRecover2FARequest, AuthRecover2FARequestBuilder> {
  @BuiltValueField(wireName: r'email')
  String get email;

  @BuiltValueField(wireName: r'mnemonic')
  String get mnemonic;

  AuthRecover2FARequest._();

  factory AuthRecover2FARequest([void updates(AuthRecover2FARequestBuilder b)]) = _$AuthRecover2FARequest;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(AuthRecover2FARequestBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<AuthRecover2FARequest> get serializer => _$AuthRecover2FARequestSerializer();
}

class _$AuthRecover2FARequestSerializer implements PrimitiveSerializer<AuthRecover2FARequest> {
  @override
  final Iterable<Type> types = const [AuthRecover2FARequest, _$AuthRecover2FARequest];

  @override
  final String wireName = r'AuthRecover2FARequest';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    AuthRecover2FARequest object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    yield r'email';
    yield serializers.serialize(
      object.email,
      specifiedType: const FullType(String),
    );
    yield r'mnemonic';
    yield serializers.serialize(
      object.mnemonic,
      specifiedType: const FullType(String),
    );
  }

  @override
  Object serialize(
    Serializers serializers,
    AuthRecover2FARequest object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object, specifiedType: specifiedType).toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required AuthRecover2FARequestBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'email':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.email = valueDes;
          break;
        case r'mnemonic':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.mnemonic = valueDes;
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  AuthRecover2FARequest deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = AuthRecover2FARequestBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}

