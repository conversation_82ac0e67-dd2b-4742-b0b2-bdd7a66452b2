//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'auth_verify_totp200_response.g.dart';

/// AuthVerifyTotp200Response
///
/// Properties:
/// * [success] 
@BuiltValue()
abstract class AuthVerifyTotp200Response implements Built<AuthVerifyTotp200Response, AuthVerifyTotp200ResponseBuilder> {
  @BuiltValueField(wireName: r'success')
  bool get success;

  AuthVerifyTotp200Response._();

  factory AuthVerifyTotp200Response([void updates(AuthVerifyTotp200ResponseBuilder b)]) = _$AuthVerifyTotp200Response;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(AuthVerifyTotp200ResponseBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<AuthVerifyTotp200Response> get serializer => _$AuthVerifyTotp200ResponseSerializer();
}

class _$AuthVerifyTotp200ResponseSerializer implements PrimitiveSerializer<AuthVerifyTotp200Response> {
  @override
  final Iterable<Type> types = const [AuthVerifyTotp200Response, _$AuthVerifyTotp200Response];

  @override
  final String wireName = r'AuthVerifyTotp200Response';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    AuthVerifyTotp200Response object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    yield r'success';
    yield serializers.serialize(
      object.success,
      specifiedType: const FullType(bool),
    );
  }

  @override
  Object serialize(
    Serializers serializers,
    AuthVerifyTotp200Response object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object, specifiedType: specifiedType).toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required AuthVerifyTotp200ResponseBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'success':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(bool),
          ) as bool;
          result.success = valueDes;
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  AuthVerifyTotp200Response deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = AuthVerifyTotp200ResponseBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}

