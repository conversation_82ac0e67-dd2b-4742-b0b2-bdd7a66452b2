//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'me_delete_session200_response.g.dart';

/// MeDeleteSession200Response
///
/// Properties:
/// * [id] 
@BuiltValue()
abstract class MeDeleteSession200Response implements Built<MeDeleteSession200Response, MeDeleteSession200ResponseBuilder> {
  @BuiltValueField(wireName: r'id')
  String get id;

  MeDeleteSession200Response._();

  factory MeDeleteSession200Response([void updates(MeDeleteSession200ResponseBuilder b)]) = _$MeDeleteSession200Response;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(MeDeleteSession200ResponseBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<MeDeleteSession200Response> get serializer => _$MeDeleteSession200ResponseSerializer();
}

class _$MeDeleteSession200ResponseSerializer implements PrimitiveSerializer<MeDeleteSession200Response> {
  @override
  final Iterable<Type> types = const [MeDeleteSession200Response, _$MeDeleteSession200Response];

  @override
  final String wireName = r'MeDeleteSession200Response';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    MeDeleteSession200Response object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    yield r'id';
    yield serializers.serialize(
      object.id,
      specifiedType: const FullType(String),
    );
  }

  @override
  Object serialize(
    Serializers serializers,
    MeDeleteSession200Response object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object, specifiedType: specifiedType).toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required MeDeleteSession200ResponseBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'id':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.id = valueDes;
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  MeDeleteSession200Response deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = MeDeleteSession200ResponseBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}

