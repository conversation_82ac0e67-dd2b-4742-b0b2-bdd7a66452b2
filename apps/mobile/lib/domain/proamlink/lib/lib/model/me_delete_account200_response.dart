//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:proamlink_api/lib/model/me_delete_session200_response.dart';
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'me_delete_account200_response.g.dart';

/// MeDeleteAccount200Response
///
/// Properties:
/// * [user] 
@BuiltValue()
abstract class MeDeleteAccount200Response implements Built<MeDeleteAccount200Response, MeDeleteAccount200ResponseBuilder> {
  @BuiltValueField(wireName: r'user')
  MeDeleteSession200Response get user;

  MeDeleteAccount200Response._();

  factory MeDeleteAccount200Response([void updates(MeDeleteAccount200ResponseBuilder b)]) = _$MeDeleteAccount200Response;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(MeDeleteAccount200ResponseBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<MeDeleteAccount200Response> get serializer => _$MeDeleteAccount200ResponseSerializer();
}

class _$MeDeleteAccount200ResponseSerializer implements PrimitiveSerializer<MeDeleteAccount200Response> {
  @override
  final Iterable<Type> types = const [MeDeleteAccount200Response, _$MeDeleteAccount200Response];

  @override
  final String wireName = r'MeDeleteAccount200Response';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    MeDeleteAccount200Response object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    yield r'user';
    yield serializers.serialize(
      object.user,
      specifiedType: const FullType(MeDeleteSession200Response),
    );
  }

  @override
  Object serialize(
    Serializers serializers,
    MeDeleteAccount200Response object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object, specifiedType: specifiedType).toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required MeDeleteAccount200ResponseBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'user':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(MeDeleteSession200Response),
          ) as MeDeleteSession200Response;
          result.user.replace(valueDes);
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  MeDeleteAccount200Response deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = MeDeleteAccount200ResponseBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}

