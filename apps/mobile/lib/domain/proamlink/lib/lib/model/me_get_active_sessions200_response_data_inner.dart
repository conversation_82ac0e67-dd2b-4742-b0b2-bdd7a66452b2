//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'me_get_active_sessions200_response_data_inner.g.dart';

/// MeGetActiveSessions200ResponseDataInner
///
/// Properties:
/// * [id] 
/// * [sessionToken] 
/// * [userId] 
/// * [expires] 
/// * [ua] 
/// * [ip] 
/// * [lastUsedAt] 
/// * [createdAt] 
@BuiltValue()
abstract class MeGetActiveSessions200ResponseDataInner implements Built<MeGetActiveSessions200ResponseDataInner, MeGetActiveSessions200ResponseDataInnerBuilder> {
  @BuiltValueField(wireName: r'id')
  String get id;

  @BuiltValueField(wireName: r'sessionToken')
  String get sessionToken;

  @BuiltValueField(wireName: r'userId')
  String get userId;

  @BuiltValueField(wireName: r'expires')
  String get expires;

  @BuiltValueField(wireName: r'ua')
  String get ua;

  @BuiltValueField(wireName: r'ip')
  String get ip;

  @BuiltValueField(wireName: r'lastUsedAt')
  String? get lastUsedAt;

  @BuiltValueField(wireName: r'createdAt')
  String get createdAt;

  MeGetActiveSessions200ResponseDataInner._();

  factory MeGetActiveSessions200ResponseDataInner([void updates(MeGetActiveSessions200ResponseDataInnerBuilder b)]) = _$MeGetActiveSessions200ResponseDataInner;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(MeGetActiveSessions200ResponseDataInnerBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<MeGetActiveSessions200ResponseDataInner> get serializer => _$MeGetActiveSessions200ResponseDataInnerSerializer();
}

class _$MeGetActiveSessions200ResponseDataInnerSerializer implements PrimitiveSerializer<MeGetActiveSessions200ResponseDataInner> {
  @override
  final Iterable<Type> types = const [MeGetActiveSessions200ResponseDataInner, _$MeGetActiveSessions200ResponseDataInner];

  @override
  final String wireName = r'MeGetActiveSessions200ResponseDataInner';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    MeGetActiveSessions200ResponseDataInner object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    yield r'id';
    yield serializers.serialize(
      object.id,
      specifiedType: const FullType(String),
    );
    yield r'sessionToken';
    yield serializers.serialize(
      object.sessionToken,
      specifiedType: const FullType(String),
    );
    yield r'userId';
    yield serializers.serialize(
      object.userId,
      specifiedType: const FullType(String),
    );
    yield r'expires';
    yield serializers.serialize(
      object.expires,
      specifiedType: const FullType(String),
    );
    yield r'ua';
    yield serializers.serialize(
      object.ua,
      specifiedType: const FullType(String),
    );
    yield r'ip';
    yield serializers.serialize(
      object.ip,
      specifiedType: const FullType(String),
    );
    yield r'lastUsedAt';
    yield object.lastUsedAt == null ? null : serializers.serialize(
      object.lastUsedAt,
      specifiedType: const FullType.nullable(String),
    );
    yield r'createdAt';
    yield serializers.serialize(
      object.createdAt,
      specifiedType: const FullType(String),
    );
  }

  @override
  Object serialize(
    Serializers serializers,
    MeGetActiveSessions200ResponseDataInner object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object, specifiedType: specifiedType).toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required MeGetActiveSessions200ResponseDataInnerBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'id':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.id = valueDes;
          break;
        case r'sessionToken':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.sessionToken = valueDes;
          break;
        case r'userId':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.userId = valueDes;
          break;
        case r'expires':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.expires = valueDes;
          break;
        case r'ua':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.ua = valueDes;
          break;
        case r'ip':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.ip = valueDes;
          break;
        case r'lastUsedAt':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.lastUsedAt = valueDes;
          break;
        case r'createdAt':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.createdAt = valueDes;
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  MeGetActiveSessions200ResponseDataInner deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = MeGetActiveSessions200ResponseDataInnerBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}

