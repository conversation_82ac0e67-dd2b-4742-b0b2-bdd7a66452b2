//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'auth_generate_totp_secret200_response.g.dart';

/// AuthGenerateTotpSecret200Response
///
/// Properties:
/// * [success] 
/// * [url] 
/// * [mnemonic] 
@BuiltValue()
abstract class AuthGenerateTotpSecret200Response implements Built<AuthGenerateTotpSecret200Response, AuthGenerateTotpSecret200ResponseBuilder> {
  @BuiltValueField(wireName: r'success')
  bool get success;

  @BuiltValueField(wireName: r'url')
  String get url;

  @BuiltValueField(wireName: r'mnemonic')
  String get mnemonic;

  AuthGenerateTotpSecret200Response._();

  factory AuthGenerateTotpSecret200Response([void updates(AuthGenerateTotpSecret200ResponseBuilder b)]) = _$AuthGenerateTotpSecret200Response;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(AuthGenerateTotpSecret200ResponseBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<AuthGenerateTotpSecret200Response> get serializer => _$AuthGenerateTotpSecret200ResponseSerializer();
}

class _$AuthGenerateTotpSecret200ResponseSerializer implements PrimitiveSerializer<AuthGenerateTotpSecret200Response> {
  @override
  final Iterable<Type> types = const [AuthGenerateTotpSecret200Response, _$AuthGenerateTotpSecret200Response];

  @override
  final String wireName = r'AuthGenerateTotpSecret200Response';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    AuthGenerateTotpSecret200Response object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    yield r'success';
    yield serializers.serialize(
      object.success,
      specifiedType: const FullType(bool),
    );
    yield r'url';
    yield serializers.serialize(
      object.url,
      specifiedType: const FullType(String),
    );
    yield r'mnemonic';
    yield serializers.serialize(
      object.mnemonic,
      specifiedType: const FullType(String),
    );
  }

  @override
  Object serialize(
    Serializers serializers,
    AuthGenerateTotpSecret200Response object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object, specifiedType: specifiedType).toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required AuthGenerateTotpSecret200ResponseBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'success':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(bool),
          ) as bool;
          result.success = valueDes;
          break;
        case r'url':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.url = valueDes;
          break;
        case r'mnemonic':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.mnemonic = valueDes;
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  AuthGenerateTotpSecret200Response deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = AuthGenerateTotpSecret200ResponseBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}

