//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'me_forgot_password_request.g.dart';

/// MeForgotPasswordRequest
///
/// Properties:
/// * [email] 
@BuiltValue()
abstract class MeForgotPasswordRequest implements Built<MeForgotPasswordRequest, MeForgotPasswordRequestBuilder> {
  @BuiltValueField(wireName: r'email')
  String get email;

  MeForgotPasswordRequest._();

  factory MeForgotPasswordRequest([void updates(MeForgotPasswordRequestBuilder b)]) = _$MeForgotPasswordRequest;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(MeForgotPasswordRequestBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<MeForgotPasswordRequest> get serializer => _$MeForgotPasswordRequestSerializer();
}

class _$MeForgotPasswordRequestSerializer implements PrimitiveSerializer<MeForgotPasswordRequest> {
  @override
  final Iterable<Type> types = const [MeForgotPasswordRequest, _$MeForgotPasswordRequest];

  @override
  final String wireName = r'MeForgotPasswordRequest';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    MeForgotPasswordRequest object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    yield r'email';
    yield serializers.serialize(
      object.email,
      specifiedType: const FullType(String),
    );
  }

  @override
  Object serialize(
    Serializers serializers,
    MeForgotPasswordRequest object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object, specifiedType: specifiedType).toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required MeForgotPasswordRequestBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'email':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.email = valueDes;
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  MeForgotPasswordRequest deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = MeForgotPasswordRequestBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}

