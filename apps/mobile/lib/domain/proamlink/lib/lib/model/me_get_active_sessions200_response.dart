//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:proamlink_api/lib/model/me_get_active_sessions200_response_data_inner.dart';
import 'package:built_collection/built_collection.dart';
import 'package:proamlink_api/lib/model/me_get_active_sessions200_response_meta.dart';
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'me_get_active_sessions200_response.g.dart';

/// MeGetActiveSessions200Response
///
/// Properties:
/// * [data] 
/// * [meta] 
@BuiltValue()
abstract class MeGetActiveSessions200Response implements Built<MeGetActiveSessions200Response, MeGetActiveSessions200ResponseBuilder> {
  @BuiltValueField(wireName: r'data')
  BuiltList<MeGetActiveSessions200ResponseDataInner>? get data;

  @BuiltValueField(wireName: r'meta')
  MeGetActiveSessions200ResponseMeta get meta;

  MeGetActiveSessions200Response._();

  factory MeGetActiveSessions200Response([void updates(MeGetActiveSessions200ResponseBuilder b)]) = _$MeGetActiveSessions200Response;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(MeGetActiveSessions200ResponseBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<MeGetActiveSessions200Response> get serializer => _$MeGetActiveSessions200ResponseSerializer();
}

class _$MeGetActiveSessions200ResponseSerializer implements PrimitiveSerializer<MeGetActiveSessions200Response> {
  @override
  final Iterable<Type> types = const [MeGetActiveSessions200Response, _$MeGetActiveSessions200Response];

  @override
  final String wireName = r'MeGetActiveSessions200Response';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    MeGetActiveSessions200Response object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    if (object.data != null) {
      yield r'data';
      yield serializers.serialize(
        object.data,
        specifiedType: const FullType(BuiltList, [FullType(MeGetActiveSessions200ResponseDataInner)]),
      );
    }
    yield r'meta';
    yield serializers.serialize(
      object.meta,
      specifiedType: const FullType(MeGetActiveSessions200ResponseMeta),
    );
  }

  @override
  Object serialize(
    Serializers serializers,
    MeGetActiveSessions200Response object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object, specifiedType: specifiedType).toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required MeGetActiveSessions200ResponseBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'data':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(BuiltList, [FullType(MeGetActiveSessions200ResponseDataInner)]),
          ) as BuiltList<MeGetActiveSessions200ResponseDataInner>;
          result.data.replace(valueDes);
          break;
        case r'meta':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(MeGetActiveSessions200ResponseMeta),
          ) as MeGetActiveSessions200ResponseMeta;
          result.meta.replace(valueDes);
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  MeGetActiveSessions200Response deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = MeGetActiveSessions200ResponseBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}

