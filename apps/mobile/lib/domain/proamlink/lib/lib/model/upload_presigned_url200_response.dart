//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:built_collection/built_collection.dart';
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'upload_presigned_url200_response.g.dart';

/// UploadPresignedUrl200Response
///
/// Properties:
/// * [url] 
/// * [fields] 
@BuiltValue()
abstract class UploadPresignedUrl200Response implements Built<UploadPresignedUrl200Response, UploadPresignedUrl200ResponseBuilder> {
  @BuiltValueField(wireName: r'url')
  String get url;

  @BuiltValueField(wireName: r'fields')
  BuiltMap<String, String> get fields;

  UploadPresignedUrl200Response._();

  factory UploadPresignedUrl200Response([void updates(UploadPresignedUrl200ResponseBuilder b)]) = _$UploadPresignedUrl200Response;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(UploadPresignedUrl200ResponseBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<UploadPresignedUrl200Response> get serializer => _$UploadPresignedUrl200ResponseSerializer();
}

class _$UploadPresignedUrl200ResponseSerializer implements PrimitiveSerializer<UploadPresignedUrl200Response> {
  @override
  final Iterable<Type> types = const [UploadPresignedUrl200Response, _$UploadPresignedUrl200Response];

  @override
  final String wireName = r'UploadPresignedUrl200Response';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    UploadPresignedUrl200Response object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    yield r'url';
    yield serializers.serialize(
      object.url,
      specifiedType: const FullType(String),
    );
    yield r'fields';
    yield serializers.serialize(
      object.fields,
      specifiedType: const FullType(BuiltMap, [FullType(String), FullType(String)]),
    );
  }

  @override
  Object serialize(
    Serializers serializers,
    UploadPresignedUrl200Response object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object, specifiedType: specifiedType).toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required UploadPresignedUrl200ResponseBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'url':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.url = valueDes;
          break;
        case r'fields':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(BuiltMap, [FullType(String), FullType(String)]),
          ) as BuiltMap<String, String>;
          result.fields.replace(valueDes);
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  UploadPresignedUrl200Response deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = UploadPresignedUrl200ResponseBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}

