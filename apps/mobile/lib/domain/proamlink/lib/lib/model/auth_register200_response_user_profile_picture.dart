//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'auth_register200_response_user_profile_picture.g.dart';

/// AuthRegister200ResponseUserProfilePicture
///
/// Properties:
/// * [id] 
/// * [bucket] 
/// * [endpoint] 
/// * [key] 
/// * [filetype] 
/// * [createdAt] 
/// * [updatedAt] 
@BuiltValue()
abstract class AuthRegister200ResponseUserProfilePicture implements Built<AuthRegister200ResponseUserProfilePicture, AuthRegister200ResponseUserProfilePictureBuilder> {
  @BuiltValueField(wireName: r'id')
  String get id;

  @BuiltValueField(wireName: r'bucket')
  String get bucket;

  @BuiltValueField(wireName: r'endpoint')
  String get endpoint;

  @BuiltValueField(wireName: r'key')
  String get key;

  @BuiltValueField(wireName: r'filetype')
  String get filetype;

  @BuiltValueField(wireName: r'createdAt')
  String get createdAt;

  @BuiltValueField(wireName: r'updatedAt')
  String get updatedAt;

  AuthRegister200ResponseUserProfilePicture._();

  factory AuthRegister200ResponseUserProfilePicture([void updates(AuthRegister200ResponseUserProfilePictureBuilder b)]) = _$AuthRegister200ResponseUserProfilePicture;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(AuthRegister200ResponseUserProfilePictureBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<AuthRegister200ResponseUserProfilePicture> get serializer => _$AuthRegister200ResponseUserProfilePictureSerializer();
}

class _$AuthRegister200ResponseUserProfilePictureSerializer implements PrimitiveSerializer<AuthRegister200ResponseUserProfilePicture> {
  @override
  final Iterable<Type> types = const [AuthRegister200ResponseUserProfilePicture, _$AuthRegister200ResponseUserProfilePicture];

  @override
  final String wireName = r'AuthRegister200ResponseUserProfilePicture';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    AuthRegister200ResponseUserProfilePicture object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    yield r'id';
    yield serializers.serialize(
      object.id,
      specifiedType: const FullType(String),
    );
    yield r'bucket';
    yield serializers.serialize(
      object.bucket,
      specifiedType: const FullType(String),
    );
    yield r'endpoint';
    yield serializers.serialize(
      object.endpoint,
      specifiedType: const FullType(String),
    );
    yield r'key';
    yield serializers.serialize(
      object.key,
      specifiedType: const FullType(String),
    );
    yield r'filetype';
    yield serializers.serialize(
      object.filetype,
      specifiedType: const FullType(String),
    );
    yield r'createdAt';
    yield serializers.serialize(
      object.createdAt,
      specifiedType: const FullType(String),
    );
    yield r'updatedAt';
    yield serializers.serialize(
      object.updatedAt,
      specifiedType: const FullType(String),
    );
  }

  @override
  Object serialize(
    Serializers serializers,
    AuthRegister200ResponseUserProfilePicture object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object, specifiedType: specifiedType).toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required AuthRegister200ResponseUserProfilePictureBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'id':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.id = valueDes;
          break;
        case r'bucket':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.bucket = valueDes;
          break;
        case r'endpoint':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.endpoint = valueDes;
          break;
        case r'key':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.key = valueDes;
          break;
        case r'filetype':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.filetype = valueDes;
          break;
        case r'createdAt':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.createdAt = valueDes;
          break;
        case r'updatedAt':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.updatedAt = valueDes;
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  AuthRegister200ResponseUserProfilePicture deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = AuthRegister200ResponseUserProfilePictureBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}

