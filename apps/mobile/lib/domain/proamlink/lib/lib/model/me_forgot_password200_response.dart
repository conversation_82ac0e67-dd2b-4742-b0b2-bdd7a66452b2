//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'me_forgot_password200_response.g.dart';

/// MeForgotPassword200Response
///
/// Properties:
/// * [email] 
@BuiltValue()
abstract class MeForgotPassword200Response implements Built<MeForgotPassword200Response, MeForgotPassword200ResponseBuilder> {
  @BuiltValueField(wireName: r'email')
  String get email;

  MeForgotPassword200Response._();

  factory MeForgotPassword200Response([void updates(MeForgotPassword200ResponseBuilder b)]) = _$MeForgotPassword200Response;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(MeForgotPassword200ResponseBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<MeForgotPassword200Response> get serializer => _$MeForgotPassword200ResponseSerializer();
}

class _$MeForgotPassword200ResponseSerializer implements PrimitiveSerializer<MeForgotPassword200Response> {
  @override
  final Iterable<Type> types = const [MeForgotPassword200Response, _$MeForgotPassword200Response];

  @override
  final String wireName = r'MeForgotPassword200Response';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    MeForgotPassword200Response object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    yield r'email';
    yield serializers.serialize(
      object.email,
      specifiedType: const FullType(String),
    );
  }

  @override
  Object serialize(
    Serializers serializers,
    MeForgotPassword200Response object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object, specifiedType: specifiedType).toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required MeForgotPassword200ResponseBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'email':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.email = valueDes;
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  MeForgotPassword200Response deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = MeForgotPassword200ResponseBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}

