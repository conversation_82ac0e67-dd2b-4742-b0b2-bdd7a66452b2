//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'dart:core';
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';
import 'package:one_of/any_of.dart';

part 'me_update_user_request_username.g.dart';

/// MeUpdateUserRequestUsername
@BuiltValue()
abstract class MeUpdateUserRequestUsername implements Built<MeUpdateUserRequestUsername, MeUpdateUserRequestUsernameBuilder> {
  /// Any Of [String]
  AnyOf get anyOf;

  MeUpdateUserRequestUsername._();

  factory MeUpdateUserRequestUsername([void updates(MeUpdateUserRequestUsernameBuilder b)]) = _$MeUpdateUserRequestUsername;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(MeUpdateUserRequestUsernameBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<MeUpdateUserRequestUsername> get serializer => _$MeUpdateUserRequestUsernameSerializer();
}

class _$MeUpdateUserRequestUsernameSerializer implements PrimitiveSerializer<MeUpdateUserRequestUsername> {
  @override
  final Iterable<Type> types = const [MeUpdateUserRequestUsername, _$MeUpdateUserRequestUsername];

  @override
  final String wireName = r'MeUpdateUserRequestUsername';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    MeUpdateUserRequestUsername object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
  }

  @override
  Object serialize(
    Serializers serializers,
    MeUpdateUserRequestUsername object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final anyOf = object.anyOf;
    return serializers.serialize(anyOf, specifiedType: FullType(AnyOf, anyOf.valueTypes.map((type) => FullType(type)).toList()))!;
  }

  @override
  MeUpdateUserRequestUsername deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = MeUpdateUserRequestUsernameBuilder();
    Object? anyOfDataSrc;
    final targetType = const FullType(AnyOf, [FullType(String), FullType(AnyOf1Enum), ]);
    anyOfDataSrc = serialized;
    result.anyOf = serializers.deserialize(anyOfDataSrc, specifiedType: targetType) as AnyOf;
    return result.build();
  }
}

