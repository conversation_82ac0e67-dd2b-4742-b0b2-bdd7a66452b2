//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:proamlink_api/lib/model/error_badrequest_issues_inner.dart';
import 'package:built_collection/built_collection.dart';
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'error_internalservererror.g.dart';

/// The error information
///
/// Properties:
/// * [message] - The error message
/// * [code] - The error code
/// * [issues] - An array of issues that were responsible for the error
@BuiltValue()
abstract class ErrorINTERNALSERVERERROR implements Built<ErrorINTERNALSERVERERROR, ErrorINTERNALSERVERERRORBuilder> {
  /// The error message
  @BuiltValueField(wireName: r'message')
  String get message;

  /// The error code
  @BuiltValueField(wireName: r'code')
  String get code;

  /// An array of issues that were responsible for the error
  @BuiltValueField(wireName: r'issues')
  BuiltList<ErrorBADREQUESTIssuesInner>? get issues;

  ErrorINTERNALSERVERERROR._();

  factory ErrorINTERNALSERVERERROR([void updates(ErrorINTERNALSERVERERRORBuilder b)]) = _$ErrorINTERNALSERVERERROR;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(ErrorINTERNALSERVERERRORBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<ErrorINTERNALSERVERERROR> get serializer => _$ErrorINTERNALSERVERERRORSerializer();
}

class _$ErrorINTERNALSERVERERRORSerializer implements PrimitiveSerializer<ErrorINTERNALSERVERERROR> {
  @override
  final Iterable<Type> types = const [ErrorINTERNALSERVERERROR, _$ErrorINTERNALSERVERERROR];

  @override
  final String wireName = r'ErrorINTERNALSERVERERROR';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    ErrorINTERNALSERVERERROR object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    yield r'message';
    yield serializers.serialize(
      object.message,
      specifiedType: const FullType(String),
    );
    yield r'code';
    yield serializers.serialize(
      object.code,
      specifiedType: const FullType(String),
    );
    if (object.issues != null) {
      yield r'issues';
      yield serializers.serialize(
        object.issues,
        specifiedType: const FullType(BuiltList, [FullType(ErrorBADREQUESTIssuesInner)]),
      );
    }
  }

  @override
  Object serialize(
    Serializers serializers,
    ErrorINTERNALSERVERERROR object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object, specifiedType: specifiedType).toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required ErrorINTERNALSERVERERRORBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'message':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.message = valueDes;
          break;
        case r'code':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.code = valueDes;
          break;
        case r'issues':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(BuiltList, [FullType(ErrorBADREQUESTIssuesInner)]),
          ) as BuiltList<ErrorBADREQUESTIssuesInner>;
          result.issues.replace(valueDes);
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  ErrorINTERNALSERVERERROR deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = ErrorINTERNALSERVERERRORBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}

