//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:proamlink_api/lib/model/me_update_user_request_username.dart';
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'me_update_user_request.g.dart';

/// MeUpdateUserRequest
///
/// Properties:
/// * [username] 
/// * [profilePictureKey] 
@BuiltValue()
abstract class MeUpdateUserRequest implements Built<MeUpdateUserRequest, MeUpdateUserRequestBuilder> {
  @BuiltValueField(wireName: r'username')
  MeUpdateUserRequestUsername? get username;

  @BuiltValueField(wireName: r'profilePictureKey')
  String? get profilePictureKey;

  MeUpdateUserRequest._();

  factory MeUpdateUserRequest([void updates(MeUpdateUserRequestBuilder b)]) = _$MeUpdateUserRequest;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(MeUpdateUserRequestBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<MeUpdateUserRequest> get serializer => _$MeUpdateUserRequestSerializer();
}

class _$MeUpdateUserRequestSerializer implements PrimitiveSerializer<MeUpdateUserRequest> {
  @override
  final Iterable<Type> types = const [MeUpdateUserRequest, _$MeUpdateUserRequest];

  @override
  final String wireName = r'MeUpdateUserRequest';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    MeUpdateUserRequest object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    if (object.username != null) {
      yield r'username';
      yield serializers.serialize(
        object.username,
        specifiedType: const FullType(MeUpdateUserRequestUsername),
      );
    }
    if (object.profilePictureKey != null) {
      yield r'profilePictureKey';
      yield serializers.serialize(
        object.profilePictureKey,
        specifiedType: const FullType.nullable(String),
      );
    }
  }

  @override
  Object serialize(
    Serializers serializers,
    MeUpdateUserRequest object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object, specifiedType: specifiedType).toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required MeUpdateUserRequestBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'username':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(MeUpdateUserRequestUsername),
          ) as MeUpdateUserRequestUsername;
          result.username.replace(valueDes);
          break;
        case r'profilePictureKey':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.profilePictureKey = valueDes;
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  MeUpdateUserRequest deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = MeUpdateUserRequestBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}

