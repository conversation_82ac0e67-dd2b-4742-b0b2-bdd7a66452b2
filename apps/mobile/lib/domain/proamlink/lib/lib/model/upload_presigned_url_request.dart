//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'upload_presigned_url_request.g.dart';

/// UploadPresignedUrlRequest
///
/// Properties:
/// * [filename] 
/// * [filetype] 
@BuiltValue()
abstract class UploadPresignedUrlRequest implements Built<UploadPresignedUrlRequest, UploadPresignedUrlRequestBuilder> {
  @BuiltValueField(wireName: r'filename')
  String get filename;

  @BuiltValueField(wireName: r'filetype')
  String get filetype;

  UploadPresignedUrlRequest._();

  factory UploadPresignedUrlRequest([void updates(UploadPresignedUrlRequestBuilder b)]) = _$UploadPresignedUrlRequest;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(UploadPresignedUrlRequestBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<UploadPresignedUrlRequest> get serializer => _$UploadPresignedUrlRequestSerializer();
}

class _$UploadPresignedUrlRequestSerializer implements PrimitiveSerializer<UploadPresignedUrlRequest> {
  @override
  final Iterable<Type> types = const [UploadPresignedUrlRequest, _$UploadPresignedUrlRequest];

  @override
  final String wireName = r'UploadPresignedUrlRequest';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    UploadPresignedUrlRequest object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    yield r'filename';
    yield serializers.serialize(
      object.filename,
      specifiedType: const FullType(String),
    );
    yield r'filetype';
    yield serializers.serialize(
      object.filetype,
      specifiedType: const FullType(String),
    );
  }

  @override
  Object serialize(
    Serializers serializers,
    UploadPresignedUrlRequest object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object, specifiedType: specifiedType).toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required UploadPresignedUrlRequestBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'filename':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.filename = valueDes;
          break;
        case r'filetype':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.filetype = valueDes;
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  UploadPresignedUrlRequest deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = UploadPresignedUrlRequestBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}

