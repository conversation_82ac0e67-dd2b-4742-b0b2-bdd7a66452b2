//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:proamlink_api/lib/model/auth_register200_response_user_profile_picture.dart';
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'auth_register200_response_user.g.dart';

/// AuthRegister200ResponseUser
///
/// Properties:
/// * [id] 
/// * [name] 
/// * [email] 
/// * [emailVerified] 
/// * [profilePicture] 
/// * [username] 
/// * [role] 
/// * [hasPassword] 
/// * [otpVerified] 
/// * [lastLocale] 
@BuiltValue()
abstract class AuthRegister200ResponseUser implements Built<AuthRegister200ResponseUser, AuthRegister200ResponseUserBuilder> {
  @BuiltValueField(wireName: r'id')
  String get id;

  @BuiltValueField(wireName: r'name')
  String? get name;

  @BuiltValueField(wireName: r'email')
  String? get email;

  @BuiltValueField(wireName: r'emailVerified')
  String? get emailVerified;

  @BuiltValueField(wireName: r'profilePicture')
  AuthRegister200ResponseUserProfilePicture? get profilePicture;

  @BuiltValueField(wireName: r'username')
  String? get username;

  @BuiltValueField(wireName: r'role')
  String get role;

  @BuiltValueField(wireName: r'hasPassword')
  bool get hasPassword;

  @BuiltValueField(wireName: r'otpVerified')
  bool get otpVerified;

  @BuiltValueField(wireName: r'lastLocale')
  String? get lastLocale;

  AuthRegister200ResponseUser._();

  factory AuthRegister200ResponseUser([void updates(AuthRegister200ResponseUserBuilder b)]) = _$AuthRegister200ResponseUser;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(AuthRegister200ResponseUserBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<AuthRegister200ResponseUser> get serializer => _$AuthRegister200ResponseUserSerializer();
}

class _$AuthRegister200ResponseUserSerializer implements PrimitiveSerializer<AuthRegister200ResponseUser> {
  @override
  final Iterable<Type> types = const [AuthRegister200ResponseUser, _$AuthRegister200ResponseUser];

  @override
  final String wireName = r'AuthRegister200ResponseUser';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    AuthRegister200ResponseUser object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    yield r'id';
    yield serializers.serialize(
      object.id,
      specifiedType: const FullType(String),
    );
    yield r'name';
    yield object.name == null ? null : serializers.serialize(
      object.name,
      specifiedType: const FullType.nullable(String),
    );
    yield r'email';
    yield object.email == null ? null : serializers.serialize(
      object.email,
      specifiedType: const FullType.nullable(String),
    );
    yield r'emailVerified';
    yield object.emailVerified == null ? null : serializers.serialize(
      object.emailVerified,
      specifiedType: const FullType.nullable(String),
    );
    yield r'profilePicture';
    yield object.profilePicture == null ? null : serializers.serialize(
      object.profilePicture,
      specifiedType: const FullType.nullable(AuthRegister200ResponseUserProfilePicture),
    );
    yield r'username';
    yield object.username == null ? null : serializers.serialize(
      object.username,
      specifiedType: const FullType.nullable(String),
    );
    yield r'role';
    yield serializers.serialize(
      object.role,
      specifiedType: const FullType(String),
    );
    yield r'hasPassword';
    yield serializers.serialize(
      object.hasPassword,
      specifiedType: const FullType(bool),
    );
    yield r'otpVerified';
    yield serializers.serialize(
      object.otpVerified,
      specifiedType: const FullType(bool),
    );
    yield r'lastLocale';
    yield object.lastLocale == null ? null : serializers.serialize(
      object.lastLocale,
      specifiedType: const FullType.nullable(String),
    );
  }

  @override
  Object serialize(
    Serializers serializers,
    AuthRegister200ResponseUser object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object, specifiedType: specifiedType).toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required AuthRegister200ResponseUserBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'id':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.id = valueDes;
          break;
        case r'name':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.name = valueDes;
          break;
        case r'email':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.email = valueDes;
          break;
        case r'emailVerified':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.emailVerified = valueDes;
          break;
        case r'profilePicture':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(AuthRegister200ResponseUserProfilePicture),
          ) as AuthRegister200ResponseUserProfilePicture?;
          if (valueDes == null) continue;
          result.profilePicture.replace(valueDes);
          break;
        case r'username':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.username = valueDes;
          break;
        case r'role':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.role = valueDes;
          break;
        case r'hasPassword':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(bool),
          ) as bool;
          result.hasPassword = valueDes;
          break;
        case r'otpVerified':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(bool),
          ) as bool;
          result.otpVerified = valueDes;
          break;
        case r'lastLocale':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType.nullable(String),
          ) as String?;
          if (valueDes == null) continue;
          result.lastLocale = valueDes;
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  AuthRegister200ResponseUser deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = AuthRegister200ResponseUserBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}

