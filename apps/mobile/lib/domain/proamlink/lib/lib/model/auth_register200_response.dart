//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:proamlink_api/lib/model/auth_register200_response_user.dart';
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'auth_register200_response.g.dart';

/// AuthRegister200Response
///
/// Properties:
/// * [user] 
@BuiltValue()
abstract class AuthRegister200Response implements Built<AuthRegister200Response, AuthRegister200ResponseBuilder> {
  @BuiltValueField(wireName: r'user')
  AuthRegister200ResponseUser get user;

  AuthRegister200Response._();

  factory AuthRegister200Response([void updates(AuthRegister200ResponseBuilder b)]) = _$AuthRegister200Response;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(AuthRegister200ResponseBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<AuthRegister200Response> get serializer => _$AuthRegister200ResponseSerializer();
}

class _$AuthRegister200ResponseSerializer implements PrimitiveSerializer<AuthRegister200Response> {
  @override
  final Iterable<Type> types = const [AuthRegister200Response, _$AuthRegister200Response];

  @override
  final String wireName = r'AuthRegister200Response';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    AuthRegister200Response object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    yield r'user';
    yield serializers.serialize(
      object.user,
      specifiedType: const FullType(AuthRegister200ResponseUser),
    );
  }

  @override
  Object serialize(
    Serializers serializers,
    AuthRegister200Response object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object, specifiedType: specifiedType).toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required AuthRegister200ResponseBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'user':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(AuthRegister200ResponseUser),
          ) as AuthRegister200ResponseUser;
          result.user.replace(valueDes);
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  AuthRegister200Response deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = AuthRegister200ResponseBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}

