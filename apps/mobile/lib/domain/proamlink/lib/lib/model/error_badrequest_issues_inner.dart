//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'error_badrequest_issues_inner.g.dart';

/// ErrorBADREQUESTIssuesInner
///
/// Properties:
/// * [message] 
@BuiltValue()
abstract class ErrorBADREQUESTIssuesInner implements Built<ErrorBADREQUESTIssuesInner, ErrorBADREQUESTIssuesInnerBuilder> {
  @BuiltValueField(wireName: r'message')
  String get message;

  ErrorBADREQUESTIssuesInner._();

  factory ErrorBADREQUESTIssuesInner([void updates(ErrorBADREQUESTIssuesInnerBuilder b)]) = _$ErrorBADREQUESTIssuesInner;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(ErrorBADREQUESTIssuesInnerBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<ErrorBADREQUESTIssuesInner> get serializer => _$ErrorBADREQUESTIssuesInnerSerializer();
}

class _$ErrorBADREQUESTIssuesInnerSerializer implements PrimitiveSerializer<ErrorBADREQUESTIssuesInner> {
  @override
  final Iterable<Type> types = const [ErrorBADREQUESTIssuesInner, _$ErrorBADREQUESTIssuesInner];

  @override
  final String wireName = r'ErrorBADREQUESTIssuesInner';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    ErrorBADREQUESTIssuesInner object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    yield r'message';
    yield serializers.serialize(
      object.message,
      specifiedType: const FullType(String),
    );
  }

  @override
  Object serialize(
    Serializers serializers,
    ErrorBADREQUESTIssuesInner object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object, specifiedType: specifiedType).toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required ErrorBADREQUESTIssuesInnerBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'message':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.message = valueDes;
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  ErrorBADREQUESTIssuesInner deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = ErrorBADREQUESTIssuesInnerBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}

