//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'me_get_active_sessions200_response_meta.g.dart';

/// MeGetActiveSessions200ResponseMeta
///
/// Properties:
/// * [total] 
/// * [page] 
/// * [perPage] 
/// * [totalPages] 
@BuiltValue()
abstract class MeGetActiveSessions200ResponseMeta implements Built<MeGetActiveSessions200ResponseMeta, MeGetActiveSessions200ResponseMetaBuilder> {
  @BuiltValueField(wireName: r'total')
  num get total;

  @BuiltValueField(wireName: r'page')
  num get page;

  @BuiltValueField(wireName: r'perPage')
  num get perPage;

  @BuiltValueField(wireName: r'totalPages')
  num get totalPages;

  MeGetActiveSessions200ResponseMeta._();

  factory MeGetActiveSessions200ResponseMeta([void updates(MeGetActiveSessions200ResponseMetaBuilder b)]) = _$MeGetActiveSessions200ResponseMeta;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(MeGetActiveSessions200ResponseMetaBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<MeGetActiveSessions200ResponseMeta> get serializer => _$MeGetActiveSessions200ResponseMetaSerializer();
}

class _$MeGetActiveSessions200ResponseMetaSerializer implements PrimitiveSerializer<MeGetActiveSessions200ResponseMeta> {
  @override
  final Iterable<Type> types = const [MeGetActiveSessions200ResponseMeta, _$MeGetActiveSessions200ResponseMeta];

  @override
  final String wireName = r'MeGetActiveSessions200ResponseMeta';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    MeGetActiveSessions200ResponseMeta object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    yield r'total';
    yield serializers.serialize(
      object.total,
      specifiedType: const FullType(num),
    );
    yield r'page';
    yield serializers.serialize(
      object.page,
      specifiedType: const FullType(num),
    );
    yield r'perPage';
    yield serializers.serialize(
      object.perPage,
      specifiedType: const FullType(num),
    );
    yield r'totalPages';
    yield serializers.serialize(
      object.totalPages,
      specifiedType: const FullType(num),
    );
  }

  @override
  Object serialize(
    Serializers serializers,
    MeGetActiveSessions200ResponseMeta object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object, specifiedType: specifiedType).toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required MeGetActiveSessions200ResponseMetaBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'total':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(num),
          ) as num;
          result.total = valueDes;
          break;
        case r'page':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(num),
          ) as num;
          result.page = valueDes;
          break;
        case r'perPage':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(num),
          ) as num;
          result.perPage = valueDes;
          break;
        case r'totalPages':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(num),
          ) as num;
          result.totalPages = valueDes;
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  MeGetActiveSessions200ResponseMeta deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = MeGetActiveSessions200ResponseMetaBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}

