//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_element
import 'package:built_value/built_value.dart';
import 'package:built_value/serializer.dart';

part 'auth_verify_totp_request.g.dart';

/// AuthVerifyTotpRequest
///
/// Properties:
/// * [token] 
@BuiltValue()
abstract class AuthVerifyTotpRequest implements Built<AuthVerifyTotpRequest, AuthVerifyTotpRequestBuilder> {
  @BuiltValueField(wireName: r'token')
  String get token;

  AuthVerifyTotpRequest._();

  factory AuthVerifyTotpRequest([void updates(AuthVerifyTotpRequestBuilder b)]) = _$AuthVerifyTotpRequest;

  @BuiltValueHook(initializeBuilder: true)
  static void _defaults(AuthVerifyTotpRequestBuilder b) => b;

  @BuiltValueSerializer(custom: true)
  static Serializer<AuthVerifyTotpRequest> get serializer => _$AuthVerifyTotpRequestSerializer();
}

class _$AuthVerifyTotpRequestSerializer implements PrimitiveSerializer<AuthVerifyTotpRequest> {
  @override
  final Iterable<Type> types = const [AuthVerifyTotpRequest, _$AuthVerifyTotpRequest];

  @override
  final String wireName = r'AuthVerifyTotpRequest';

  Iterable<Object?> _serializeProperties(
    Serializers serializers,
    AuthVerifyTotpRequest object, {
    FullType specifiedType = FullType.unspecified,
  }) sync* {
    yield r'token';
    yield serializers.serialize(
      object.token,
      specifiedType: const FullType(String),
    );
  }

  @override
  Object serialize(
    Serializers serializers,
    AuthVerifyTotpRequest object, {
    FullType specifiedType = FullType.unspecified,
  }) {
    return _serializeProperties(serializers, object, specifiedType: specifiedType).toList();
  }

  void _deserializeProperties(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
    required List<Object?> serializedList,
    required AuthVerifyTotpRequestBuilder result,
    required List<Object?> unhandled,
  }) {
    for (var i = 0; i < serializedList.length; i += 2) {
      final key = serializedList[i] as String;
      final value = serializedList[i + 1];
      switch (key) {
        case r'token':
          final valueDes = serializers.deserialize(
            value,
            specifiedType: const FullType(String),
          ) as String;
          result.token = valueDes;
          break;
        default:
          unhandled.add(key);
          unhandled.add(value);
          break;
      }
    }
  }

  @override
  AuthVerifyTotpRequest deserialize(
    Serializers serializers,
    Object serialized, {
    FullType specifiedType = FullType.unspecified,
  }) {
    final result = AuthVerifyTotpRequestBuilder();
    final serializedList = (serialized as Iterable<Object?>).toList();
    final unhandled = <Object?>[];
    _deserializeProperties(
      serializers,
      serialized,
      specifiedType: specifiedType,
      serializedList: serializedList,
      unhandled: unhandled,
      result: result,
    );
    return result.build();
  }
}

