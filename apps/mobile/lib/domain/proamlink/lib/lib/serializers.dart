//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

// ignore_for_file: unused_import

import 'package:one_of_serializer/any_of_serializer.dart';
import 'package:one_of_serializer/one_of_serializer.dart';
import 'package:built_collection/built_collection.dart';
import 'package:built_value/json_object.dart';
import 'package:built_value/serializer.dart';
import 'package:built_value/standard_json_plugin.dart';
import 'package:built_value/iso_8601_date_time_serializer.dart';
import 'package:proamlink_api/lib/date_serializer.dart';
import 'package:proamlink_api/lib/model/date.dart';

import 'package:proamlink_api/lib/model/auth_generate_totp_secret200_response.dart';
import 'package:proamlink_api/lib/model/auth_recover2_fa_request.dart';
import 'package:proamlink_api/lib/model/auth_register200_response.dart';
import 'package:proamlink_api/lib/model/auth_register200_response_user.dart';
import 'package:proamlink_api/lib/model/auth_register200_response_user_profile_picture.dart';
import 'package:proamlink_api/lib/model/auth_register_request.dart';
import 'package:proamlink_api/lib/model/auth_verify_totp200_response.dart';
import 'package:proamlink_api/lib/model/auth_verify_totp_request.dart';
import 'package:proamlink_api/lib/model/error_badrequest.dart';
import 'package:proamlink_api/lib/model/error_badrequest_issues_inner.dart';
import 'package:proamlink_api/lib/model/error_forbidden.dart';
import 'package:proamlink_api/lib/model/error_internalservererror.dart';
import 'package:proamlink_api/lib/model/error_notfound.dart';
import 'package:proamlink_api/lib/model/error_unauthorized.dart';
import 'package:proamlink_api/lib/model/me_delete_account200_response.dart';
import 'package:proamlink_api/lib/model/me_delete_session200_response.dart';
import 'package:proamlink_api/lib/model/me_forgot_password200_response.dart';
import 'package:proamlink_api/lib/model/me_forgot_password_request.dart';
import 'package:proamlink_api/lib/model/me_get_active_sessions200_response.dart';
import 'package:proamlink_api/lib/model/me_get_active_sessions200_response_data_inner.dart';
import 'package:proamlink_api/lib/model/me_get_active_sessions200_response_meta.dart';
import 'package:proamlink_api/lib/model/me_reset_password_request.dart';
import 'package:proamlink_api/lib/model/me_update_user_request.dart';
import 'package:proamlink_api/lib/model/me_update_user_request_username.dart';
import 'package:proamlink_api/lib/model/system_health200_response.dart';
import 'package:proamlink_api/lib/model/system_health200_response_services.dart';
import 'package:proamlink_api/lib/model/upload_presigned_url200_response.dart';
import 'package:proamlink_api/lib/model/upload_presigned_url_request.dart';

part 'serializers.g.dart';

@SerializersFor([
  AuthGenerateTotpSecret200Response,
  AuthRecover2FARequest,
  AuthRegister200Response,
  AuthRegister200ResponseUser,
  AuthRegister200ResponseUserProfilePicture,
  AuthRegisterRequest,
  AuthVerifyTotp200Response,
  AuthVerifyTotpRequest,
  ErrorBADREQUEST,
  ErrorBADREQUESTIssuesInner,
  ErrorFORBIDDEN,
  ErrorINTERNALSERVERERROR,
  ErrorNOTFOUND,
  ErrorUNAUTHORIZED,
  MeDeleteAccount200Response,
  MeDeleteSession200Response,
  MeForgotPassword200Response,
  MeForgotPasswordRequest,
  MeGetActiveSessions200Response,
  MeGetActiveSessions200ResponseDataInner,
  MeGetActiveSessions200ResponseMeta,
  MeResetPasswordRequest,
  MeUpdateUserRequest,
  MeUpdateUserRequestUsername,
  SystemHealth200Response,
  SystemHealth200ResponseServices,
  UploadPresignedUrl200Response,
  UploadPresignedUrlRequest,
])
Serializers serializers = (_$serializers.toBuilder()
      ..add(const OneOfSerializer())
      ..add(const AnyOfSerializer())
      ..add(const DateSerializer())
      ..add(Iso8601DateTimeSerializer())
    ).build();

Serializers standardSerializers =
    (serializers.toBuilder()..addPlugin(StandardJsonPlugin())).build();
