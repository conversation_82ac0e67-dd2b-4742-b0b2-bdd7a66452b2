//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//

import 'dart:async';

import 'package:built_value/json_object.dart';
import 'package:built_value/serializer.dart';
import 'package:dio/dio.dart';

import 'package:proamlink_api/lib/model/error_badrequest.dart';
import 'package:proamlink_api/lib/model/error_forbidden.dart';
import 'package:proamlink_api/lib/model/error_internalservererror.dart';
import 'package:proamlink_api/lib/model/error_unauthorized.dart';
import 'package:proamlink_api/lib/model/upload_presigned_url200_response.dart';
import 'package:proamlink_api/lib/model/upload_presigned_url_request.dart';

class UploadApi {

  final Dio _dio;

  final Serializers _serializers;

  const UploadApi(this._dio, this._serializers);

  /// Génération d&#39;une URL pré-signée
  /// Génère une URL pré-signée pour l&#39;upload direct de fichiers vers S3
  ///
  /// Parameters:
  /// * [uploadPresignedUrlRequest] 
  /// * [cancelToken] - A [CancelToken] that can be used to cancel the operation
  /// * [headers] - Can be used to add additional headers to the request
  /// * [extras] - Can be used to add flags to the request
  /// * [validateStatus] - A [ValidateStatus] callback that can be used to determine request success based on the HTTP status of the response
  /// * [onSendProgress] - A [ProgressCallback] that can be used to get the send progress
  /// * [onReceiveProgress] - A [ProgressCallback] that can be used to get the receive progress
  ///
  /// Returns a [Future] containing a [Response] with a [UploadPresignedUrl200Response] as data
  /// Throws [DioException] if API call or serialization fails
  Future<Response<UploadPresignedUrl200Response>> uploadPresignedUrl({ 
    required UploadPresignedUrlRequest uploadPresignedUrlRequest,
    CancelToken? cancelToken,
    Map<String, dynamic>? headers,
    Map<String, dynamic>? extra,
    ValidateStatus? validateStatus,
    ProgressCallback? onSendProgress,
    ProgressCallback? onReceiveProgress,
  }) async {
    final _path = r'/api/rest/upload/presigned-url';
    final _options = Options(
      method: r'POST',
      headers: <String, dynamic>{
        ...?headers,
      },
      extra: <String, dynamic>{
        'secure': <Map<String, String>>[
          {
            'type': 'http',
            'scheme': 'bearer',
            'name': 'bearerAuth',
          },
        ],
        ...?extra,
      },
      contentType: 'application/json',
      validateStatus: validateStatus,
    );

    dynamic _bodyData;

    try {
      const _type = FullType(UploadPresignedUrlRequest);
      _bodyData = _serializers.serialize(uploadPresignedUrlRequest, specifiedType: _type);

    } catch(error, stackTrace) {
      throw DioException(
         requestOptions: _options.compose(
          _dio.options,
          _path,
        ),
        type: DioExceptionType.unknown,
        error: error,
        stackTrace: stackTrace,
      );
    }

    final _response = await _dio.request<Object>(
      _path,
      data: _bodyData,
      options: _options,
      cancelToken: cancelToken,
      onSendProgress: onSendProgress,
      onReceiveProgress: onReceiveProgress,
    );

    UploadPresignedUrl200Response? _responseData;

    try {
      final rawResponse = _response.data;
      _responseData = rawResponse == null ? null : _serializers.deserialize(
        rawResponse,
        specifiedType: const FullType(UploadPresignedUrl200Response),
      ) as UploadPresignedUrl200Response;

    } catch (error, stackTrace) {
      throw DioException(
        requestOptions: _response.requestOptions,
        response: _response,
        type: DioExceptionType.unknown,
        error: error,
        stackTrace: stackTrace,
      );
    }

    return Response<UploadPresignedUrl200Response>(
      data: _responseData,
      headers: _response.headers,
      isRedirect: _response.isRedirect,
      requestOptions: _response.requestOptions,
      redirects: _response.redirects,
      statusCode: _response.statusCode,
      statusMessage: _response.statusMessage,
      extra: _response.extra,
    );
  }

}
