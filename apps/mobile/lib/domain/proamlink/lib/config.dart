// Configuration automatiquement générée pour le SDK ProAmLink
// Ne pas modifier manuellement - ce fichier est régénéré automatiquement

/// Configuration par défaut pour le client API ProAmLink
class ProAmLinkConfig {
  /// URL de base de l'API (production)
  static const String baseUrlProduction = 'https://api.proamlink.com';

  /// URL de base de l'API (développement)
  static const String baseUrlDevelopment = 'http://localhost:3000';

  /// URL de base de l'API (staging)
  static const String baseUrlStaging = 'https://staging-api.proamlink.com';

  /// Timeout par défaut pour les requêtes (en millisecondes)
  static const int defaultTimeout = 30000;

  /// Version du SDK
  static const String sdkVersion = '1.0.0';

  /// Date de génération du SDK
  static const String generatedAt = '2025-08-14T15:39:03.375Z';

  /// Obtient l'URL de base selon l'environnement
  static String getBaseUrl({bool isProduction = false, bool isStaging = false}) {
    if (isProduction) return baseUrlProduction;
    if (isStaging) return baseUrlStaging;
    return baseUrlDevelopment;
  }
}