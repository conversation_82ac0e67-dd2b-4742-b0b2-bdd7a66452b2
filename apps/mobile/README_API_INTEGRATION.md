# 🚀 Intégration du SDK API ProAmLink pour Flutter

Ce guide détaille l'intégration et l'utilisation du SDK Flutter généré automatiquement à partir de l'API tRPC de ProAmLink.

## 📋 Table des matières

- [Installation](#installation)
- [Configuration](#configuration)
- [Authentification](#authentification)
- [Utilisation des endpoints](#utilisation-des-endpoints)
- [Gestion des erreurs](#gestion-des-erreurs)
- [Exemples pratiques](#exemples-pratiques)
- [Bonnes pratiques](#bonnes-pratiques)
- [Dépannage](#dépannage)

## 🔧 Installation

### 1. Génération du SDK

Le SDK est généré automatiquement à partir de la spécification OpenAPI de l'API ProAmLink :

```bash
# Depuis la racine du projet
npm run generate:flutter-sdk
```

Cette commande :
- Télécharge la spécification OpenAPI depuis `/api/openapi.json`
- <PERSON><PERSON><PERSON> le SDK Flutter avec `openapi-generator-cli`
- Place les fichiers dans `apps/mobile/lib/domain/proamlink/`

### 2. Ajout des dépendances

Ajoutez les dépendances nécessaires dans votre `pubspec.yaml` :

```yaml
dependencies:
  # Dépendances pour le SDK généré
  dio: ^5.3.0
  built_value: ^8.6.0
  built_collection: ^5.1.1
  json_annotation: ^4.8.1
  
  # Dépendances pour l'authentification
  flutter_secure_storage: ^9.0.0
  
  # Dépendances pour la gestion d'état (optionnel)
  riverpod: ^2.4.0
  flutter_riverpod: ^2.4.0

dev_dependencies:
  # Générateurs de code
  build_runner: ^2.4.6
  built_value_generator: ^8.6.0
  json_serializable: ^6.7.0
```

### 3. Installation des packages

```bash
cd apps/mobile
flutter pub get
flutter pub run build_runner build
```

## ⚙️ Configuration

### 1. Configuration de base

Créez un fichier de configuration pour l'API :

```dart
// lib/config/api_config.dart
import 'package:proamlink_api/config.dart';

class ApiConfig {
  static const bool isProduction = bool.fromEnvironment('dart.vm.product');
  static const bool isStaging = bool.fromEnvironment('STAGING', defaultValue: false);
  
  static String get baseUrl => ProAmLinkConfig.getBaseUrl(
    isProduction: isProduction,
    isStaging: isStaging,
  );
  
  static const Duration timeout = Duration(seconds: 30);
  static const Duration connectTimeout = Duration(seconds: 10);
  static const Duration receiveTimeout = Duration(seconds: 30);
}
```

### 2. Configuration du client API

```dart
// lib/services/api_client.dart
import 'package:dio/dio.dart';
import 'package:proamlink_api/proamlink_api.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import '../config/api_config.dart';

class ApiClient {
  static ApiClient? _instance;
  static ApiClient get instance => _instance ??= ApiClient._();
  
  late final ProAmLinkApiClient _client;
  late final Dio _dio;
  final FlutterSecureStorage _storage = const FlutterSecureStorage();
  
  ApiClient._() {
    _dio = Dio(BaseOptions(
      baseUrl: ApiConfig.baseUrl,
      connectTimeout: ApiConfig.connectTimeout,
      receiveTimeout: ApiConfig.receiveTimeout,
      sendTimeout: ApiConfig.timeout,
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
    ));
    
    // Intercepteur pour l'authentification
    _dio.interceptors.add(AuthInterceptor(_storage));
    
    // Intercepteur pour les logs (en développement uniquement)
    if (!ApiConfig.isProduction) {
      _dio.interceptors.add(LogInterceptor(
        requestBody: true,
        responseBody: true,
        requestHeader: true,
        responseHeader: false,
      ));
    }
    
    _client = ProAmLinkApiClient(_dio);
  }
  
  ProAmLinkApiClient get client => _client;
  
  // Méthodes d'authentification
  Future<void> setAuthToken(String token) async {
    await _storage.write(key: 'auth_token', value: token);
  }
  
  Future<void> clearAuthToken() async {
    await _storage.delete(key: 'auth_token');
  }
  
  Future<String?> getAuthToken() async {
    return await _storage.read(key: 'auth_token');
  }
}

// Intercepteur pour l'authentification automatique
class AuthInterceptor extends Interceptor {
  final FlutterSecureStorage storage;
  
  AuthInterceptor(this.storage);
  
  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) async {
    final token = await storage.read(key: 'auth_token');
    if (token != null) {
      options.headers['Authorization'] = 'Bearer $token';
    }
    handler.next(options);
  }
  
  @override
  void onError(DioException err, ErrorInterceptorHandler handler) async {
    // Gestion automatique de l'expiration du token
    if (err.response?.statusCode == 401) {
      await storage.delete(key: 'auth_token');
      // Optionnel : rediriger vers la page de connexion
    }
    handler.next(err);
  }
}
```

## 🔐 Authentification

### 1. Connexion utilisateur

```dart
// lib/services/auth_service.dart
import 'package:proamlink_api/proamlink_api.dart';
import 'api_client.dart';

class AuthService {
  final ApiClient _apiClient = ApiClient.instance;
  
  /// Connexion avec email et mot de passe
  Future<UserProfileResponse> login(String email, String password) async {
    try {
      // Utiliser l'endpoint d'authentification (à implémenter dans tRPC)
      final response = await _apiClient.client.authLogin(
        LoginRequest(email: email, password: password)
      );
      
      // Sauvegarder le token
      if (response.token != null) {
        await _apiClient.setAuthToken(response.token!);
      }
      
      return response.user;
    } catch (e) {
      throw AuthException('Erreur de connexion: ${e.toString()}');
    }
  }
  
  /// Déconnexion
  Future<void> logout() async {
    await _apiClient.clearAuthToken();
  }
  
  /// Vérifier si l'utilisateur est connecté
  Future<bool> isLoggedIn() async {
    final token = await _apiClient.getAuthToken();
    return token != null;
  }
}

class AuthException implements Exception {
  final String message;
  AuthException(this.message);
  
  @override
  String toString() => message;
}
```

### 2. Gestion automatique de l'authentification

```dart
// lib/providers/auth_provider.dart
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../services/auth_service.dart';

final authServiceProvider = Provider<AuthService>((ref) => AuthService());

final authStateProvider = StateNotifierProvider<AuthNotifier, AuthState>((ref) {
  return AuthNotifier(ref.read(authServiceProvider));
});

class AuthNotifier extends StateNotifier<AuthState> {
  final AuthService _authService;
  
  AuthNotifier(this._authService) : super(AuthState.initial()) {
    _checkAuthStatus();
  }
  
  Future<void> _checkAuthStatus() async {
    state = state.copyWith(isLoading: true);
    
    try {
      final isLoggedIn = await _authService.isLoggedIn();
      state = state.copyWith(
        isAuthenticated: isLoggedIn,
        isLoading: false,
      );
    } catch (e) {
      state = state.copyWith(
        isAuthenticated: false,
        isLoading: false,
        error: e.toString(),
      );
    }
  }
  
  Future<void> login(String email, String password) async {
    state = state.copyWith(isLoading: true, error: null);
    
    try {
      final user = await _authService.login(email, password);
      state = state.copyWith(
        isAuthenticated: true,
        user: user,
        isLoading: false,
      );
    } catch (e) {
      state = state.copyWith(
        isAuthenticated: false,
        isLoading: false,
        error: e.toString(),
      );
    }
  }
  
  Future<void> logout() async {
    await _authService.logout();
    state = AuthState.initial();
  }
}

class AuthState {
  final bool isAuthenticated;
  final bool isLoading;
  final UserProfileResponse? user;
  final String? error;
  
  AuthState({
    required this.isAuthenticated,
    required this.isLoading,
    this.user,
    this.error,
  });
  
  factory AuthState.initial() => AuthState(
    isAuthenticated: false,
    isLoading: false,
  );
  
  AuthState copyWith({
    bool? isAuthenticated,
    bool? isLoading,
    UserProfileResponse? user,
    String? error,
  }) {
    return AuthState(
      isAuthenticated: isAuthenticated ?? this.isAuthenticated,
      isLoading: isLoading ?? this.isLoading,
      user: user ?? this.user,
      error: error ?? this.error,
    );
  }
}
```

## 📡 Utilisation des endpoints

### 1. Endpoints publics

```dart
// Vérification de l'état de l'API
Future<HealthResponse> checkApiHealth() async {
  try {
    final response = await ApiClient.instance.client.openapiHealth();
    return response;
  } catch (e) {
    throw ApiException('Erreur lors de la vérification de l\'API: $e');
  }
}
```

### 2. Endpoints protégés

```dart
// Récupération du profil utilisateur
Future<UserProfileResponse> getUserProfile() async {
  try {
    final response = await ApiClient.instance.client.openapiUserProfile();
    return response;
  } catch (e) {
    throw ApiException('Erreur lors de la récupération du profil: $e');
  }
}

// Récupération d'un utilisateur par ID
Future<GetUserByIdResponse> getUserById(String userId) async {
  try {
    final response = await ApiClient.instance.client.openapiGetUserById(userId);
    return response;
  } catch (e) {
    throw ApiException('Erreur lors de la récupération de l\'utilisateur: $e');
  }
}
```

### 3. Endpoints avec body JSON

```dart
// Création d'un nouvel utilisateur
Future<CreateUserResponse> createUser({
  required String name,
  required String email,
  required String password,
  String? username,
  String role = 'user',
}) async {
  try {
    final request = CreateUserRequest(
      name: name,
      email: email,
      password: password,
      username: username,
      role: role,
    );
    
    final response = await ApiClient.instance.client.openapiCreateUser(request);
    return response;
  } catch (e) {
    throw ApiException('Erreur lors de la création de l\'utilisateur: $e');
  }
}
```

## 🚨 Gestion des erreurs

### 1. Types d'erreurs

```dart
// lib/models/api_exception.dart
class ApiException implements Exception {
  final String message;
  final int? statusCode;
  final String? errorCode;
  final dynamic details;
  
  ApiException(
    this.message, {
    this.statusCode,
    this.errorCode,
    this.details,
  });
  
  factory ApiException.fromDioError(DioException error) {
    switch (error.type) {
      case DioExceptionType.connectionTimeout:
        return ApiException('Timeout de connexion');
      case DioExceptionType.sendTimeout:
        return ApiException('Timeout d\'envoi');
      case DioExceptionType.receiveTimeout:
        return ApiException('Timeout de réception');
      case DioExceptionType.badResponse:
        return ApiException(
          'Erreur serveur',
          statusCode: error.response?.statusCode,
          details: error.response?.data,
        );
      case DioExceptionType.cancel:
        return ApiException('Requête annulée');
      default:
        return ApiException('Erreur réseau: ${error.message}');
    }
  }
  
  @override
  String toString() => message;
}
```

### 2. Wrapper pour la gestion d'erreurs

```dart
// lib/services/api_service.dart
abstract class ApiService {
  static Future<T> handleApiCall<T>(Future<T> Function() apiCall) async {
    try {
      return await apiCall();
    } on DioException catch (e) {
      throw ApiException.fromDioError(e);
    } catch (e) {
      throw ApiException('Erreur inattendue: $e');
    }
  }
}

// Utilisation
Future<UserProfileResponse> getUserProfile() async {
  return ApiService.handleApiCall(() async {
    return await ApiClient.instance.client.openapiUserProfile();
  });
}
```

## 💡 Exemples pratiques

### 1. Widget de profil utilisateur

```dart
// lib/widgets/user_profile_widget.dart
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

final userProfileProvider = FutureProvider<UserProfileResponse>((ref) async {
  return await ApiClient.instance.client.openapiUserProfile();
});

class UserProfileWidget extends ConsumerWidget {
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final profileAsync = ref.watch(userProfileProvider);
    
    return profileAsync.when(
      data: (profile) => Column(
        children: [
          Text('Nom: ${profile.user.name ?? 'Non défini'}'),
          Text('Email: ${profile.user.email}'),
          Text('Rôle: ${profile.user.role}'),
          Text('Langue: ${profile.preferences.language}'),
          Text('Thème: ${profile.preferences.theme}'),
        ],
      ),
      loading: () => const CircularProgressIndicator(),
      error: (error, stack) => Text('Erreur: $error'),
    );
  }
}
```

### 2. Service de gestion des utilisateurs

```dart
// lib/services/user_service.dart
class UserService {
  final ApiClient _apiClient = ApiClient.instance;
  
  Future<List<User>> searchUsers(String query) async {
    return ApiService.handleApiCall(() async {
      // Implémenter l'endpoint de recherche dans tRPC
      final response = await _apiClient.client.searchUsers(query);
      return response.users;
    });
  }
  
  Future<User> updateUserProfile(UpdateUserRequest request) async {
    return ApiService.handleApiCall(() async {
      final response = await _apiClient.client.updateUserProfile(request);
      return response.user;
    });
  }
}
```

## 🎯 Bonnes pratiques

### 1. Gestion du cache

```dart
// Utiliser Riverpod pour le cache automatique
final userProvider = FutureProvider.family<User, String>((ref, userId) async {
  return await ApiClient.instance.client.openapiGetUserById(userId);
});

// Cache avec invalidation
final userListProvider = StateNotifierProvider<UserListNotifier, AsyncValue<List<User>>>((ref) {
  return UserListNotifier();
});
```

### 2. Retry automatique

```dart
// Intercepteur pour retry automatique
class RetryInterceptor extends Interceptor {
  final int maxRetries;
  final Duration delay;
  
  RetryInterceptor({this.maxRetries = 3, this.delay = const Duration(seconds: 1)});
  
  @override
  void onError(DioException err, ErrorInterceptorHandler handler) async {
    if (err.requestOptions.extra['retryCount'] == null) {
      err.requestOptions.extra['retryCount'] = 0;
    }
    
    final retryCount = err.requestOptions.extra['retryCount'] as int;
    
    if (retryCount < maxRetries && _shouldRetry(err)) {
      err.requestOptions.extra['retryCount'] = retryCount + 1;
      await Future.delayed(delay);
      
      try {
        final response = await Dio().fetch(err.requestOptions);
        handler.resolve(response);
      } catch (e) {
        handler.next(err);
      }
    } else {
      handler.next(err);
    }
  }
  
  bool _shouldRetry(DioException err) {
    return err.type == DioExceptionType.connectionTimeout ||
           err.type == DioExceptionType.receiveTimeout ||
           (err.response?.statusCode ?? 0) >= 500;
  }
}
```

## 🔧 Dépannage

### Problèmes courants

1. **Erreur de génération du SDK**
   ```bash
   # Vérifier que l'API est démarrée
   curl http://localhost:3000/api/openapi.json
   
   # Régénérer le SDK
   npm run generate:flutter-sdk
   ```

2. **Erreurs d'authentification**
   ```dart
   // Vérifier le token
   final token = await ApiClient.instance.getAuthToken();
   print('Token: $token');
   ```

3. **Erreurs de sérialisation**
   ```bash
   # Régénérer les fichiers built_value
   flutter pub run build_runner build --delete-conflicting-outputs
   ```

### Logs de debugging

```dart
// Activer les logs détaillés en développement
if (kDebugMode) {
  _dio.interceptors.add(LogInterceptor(
    requestBody: true,
    responseBody: true,
    requestHeader: true,
    responseHeader: true,
    logPrint: (object) => print('API: $object'),
  ));
}
```

---

## 📞 Support

Pour toute question ou problème :
1. Vérifiez la documentation Swagger : `http://localhost:3000/api/docs`
2. Consultez les logs de l'API et de l'application Flutter
3. Contactez l'équipe de développement backend

**Version du SDK :** 1.0.0  
**Dernière mise à jour :** Automatique via `npm run generate:flutter-sdk`
