# 🚀 Guide tRPC-OpenAPI pour les développeurs

Ce guide explique comment exposer vos endpoints tRPC via REST/OpenAPI de manière optimale, sans duplication de code.

## 📋 Table des matières

- [Architecture recommandée](#architecture-recommandée)
- [Démarche pour les développeurs](#démarche-pour-les-développeurs)
- [Exemples pratiques](#exemples-pratiques)
- [Gestion des conflits](#gestion-des-conflits)
- [Bonnes pratiques](#bonnes-pratiques)

## 🏗️ Architecture recommandée

### Principe : **Une seule source de vérité**

Au lieu de créer des endpoints séparés, **annotez directement vos procédures tRPC existantes** avec les métadonnées OpenAPI. Cela garantit :

- ✅ **Pas de duplication** de code
- ✅ **Synchronisation automatique** entre tRPC et REST
- ✅ **Maintenance simplifiée**
- ✅ **Type safety** partagé

### Architecture actuelle

```
📁 apps/app/src/api/
├── 📁 auth/
│   └── _router.ts          # Router d'authentification
├── 📁 me/
│   └── _router.ts          # Router utilisateur
├── 📁 upload/
│   └── _router.ts          # Router upload
└── _app.ts                 # Router principal
```

### Architecture optimisée

```
📁 apps/app/src/api/
├── 📁 auth/
│   ├── _router.ts          # Router avec métadonnées OpenAPI
│   └── schemas.ts          # Schémas Zod partagés
├── 📁 me/
│   ├── _router.ts          # Router avec métadonnées OpenAPI
│   └── schemas.ts          # Schémas Zod partagés
├── 📁 upload/
│   ├── _router.ts          # Router avec métadonnées OpenAPI
│   └── schemas.ts          # Schémas Zod partagés
└── _app.ts                 # Router principal
```

## 👨‍💻 Démarche pour les développeurs

### 1. **Créer une procédure tRPC standard**

```typescript
// apps/app/src/api/users/_router.ts
import { z } from "zod"
import { authenticatedProcedure, router } from "@/lib/server/trpc"

export const usersRouter = router({
  getProfile: authenticatedProcedure
    .input(z.void())
    .output(z.object({
      id: z.string(),
      name: z.string().nullable(),
      email: z.string(),
    }))
    .query(async ({ ctx }) => {
      // Logique métier
      return await getUserProfile(ctx.session.user.id)
    }),
})
```

### 2. **Ajouter les métadonnées OpenAPI**

```typescript
// apps/app/src/api/users/_router.ts
export const usersRouter = router({
  getProfile: authenticatedProcedure
    .meta({
      openapi: {
        method: "GET",
        path: "/api/users/profile",
        tags: ["Utilisateurs"],
        summary: "Récupération du profil utilisateur",
        description: "Récupère les informations du profil de l'utilisateur connecté",
        protect: true, // Nécessite une authentification
      },
    })
    .input(z.void())
    .output(z.object({
      id: z.string().describe("Identifiant unique de l'utilisateur"),
      name: z.string().nullable().describe("Nom complet de l'utilisateur"),
      email: z.string().describe("Adresse email de l'utilisateur"),
    }))
    .query(async ({ ctx }) => {
      // Même logique métier
      return await getUserProfile(ctx.session.user.id)
    }),
})
```

### 3. **Utiliser des schémas Zod réutilisables**

```typescript
// apps/app/src/api/users/schemas.ts
import { z } from "zod"

export const userProfileSchema = z.object({
  id: z.string().describe("Identifiant unique de l'utilisateur"),
  name: z.string().nullable().describe("Nom complet de l'utilisateur"),
  email: z.string().describe("Adresse email de l'utilisateur"),
  role: z.string().describe("Rôle de l'utilisateur"),
  createdAt: z.string().datetime().describe("Date de création du compte"),
})

export const getUserByIdSchema = z.object({
  id: z.string().min(1).describe("Identifiant de l'utilisateur à récupérer"),
})

// apps/app/src/api/users/_router.ts
import { userProfileSchema, getUserByIdSchema } from "./schemas"

export const usersRouter = router({
  getProfile: authenticatedProcedure
    .meta({
      openapi: {
        method: "GET",
        path: "/api/users/profile",
        tags: ["Utilisateurs"],
        summary: "Récupération du profil utilisateur",
      },
    })
    .input(z.void())
    .output(userProfileSchema)
    .query(getUserProfileHandler),

  getById: authenticatedProcedure
    .meta({
      openapi: {
        method: "GET",
        path: "/api/users/{id}",
        tags: ["Utilisateurs"],
        summary: "Récupération d'un utilisateur par ID",
      },
    })
    .input(getUserByIdSchema)
    .output(userProfileSchema)
    .query(getUserByIdHandler),
})
```

### 4. **Ajouter le router au router principal**

```typescript
// apps/app/src/api/_app.ts
import { router } from "../lib/server/trpc"
import { authRouter } from "./auth/_router"
import { meRouter } from "./me/_router"
import { uploadRouter } from "./upload/_router"
import { usersRouter } from "./users/_router" // Nouveau router

export const appRouter = router({
  auth: authRouter,
  me: meRouter,
  upload: uploadRouter,
  users: usersRouter, // Ajouter le nouveau router
})
```

## 🔧 Exemples pratiques

### Endpoint public (sans authentification)

```typescript
healthCheck: publicProcedure
  .meta({
    openapi: {
      method: "GET",
      path: "/api/health",
      tags: ["Système"],
      summary: "Vérification de l'état de l'API",
      protect: false, // Endpoint public
    },
  })
  .input(z.void())
  .output(z.object({
    status: z.literal("ok"),
    timestamp: z.string(),
  }))
  .query(async () => ({
    status: "ok" as const,
    timestamp: new Date().toISOString(),
  }))
```

### Endpoint avec paramètres de chemin

```typescript
getUserById: authenticatedProcedure
  .meta({
    openapi: {
      method: "GET",
      path: "/api/users/{id}",
      tags: ["Utilisateurs"],
      summary: "Récupération d'un utilisateur par ID",
    },
  })
  .input(z.object({
    id: z.string().min(1),
  }))
  .output(userSchema)
  .query(async ({ input }) => {
    return await getUserById(input.id)
  })
```

### Endpoint POST avec body JSON

```typescript
createUser: publicProcedure
  .meta({
    openapi: {
      method: "POST",
      path: "/api/users",
      tags: ["Utilisateurs"],
      summary: "Création d'un nouvel utilisateur",
    },
  })
  .input(z.object({
    name: z.string().min(2),
    email: z.string().email(),
    password: z.string().min(8),
  }))
  .output(z.object({
    user: userSchema,
    message: z.string(),
  }))
  .mutation(async ({ input }) => {
    const user = await createUser(input)
    return {
      user,
      message: "Utilisateur créé avec succès",
    }
  })
```

### Endpoint avec query parameters

```typescript
searchUsers: authenticatedProcedure
  .meta({
    openapi: {
      method: "GET",
      path: "/api/users/search",
      tags: ["Utilisateurs"],
      summary: "Recherche d'utilisateurs",
    },
  })
  .input(z.object({
    query: z.string().min(1).describe("Terme de recherche"),
    limit: z.number().min(1).max(100).default(20).describe("Nombre de résultats"),
    offset: z.number().min(0).default(0).describe("Décalage pour la pagination"),
  }))
  .output(z.object({
    users: z.array(userSchema),
    total: z.number(),
  }))
  .query(async ({ input }) => {
    return await searchUsers(input)
  })
```

## ⚠️ Gestion des conflits

### Problème : Endpoints existants

Si vous avez déjà un endpoint `/api/health` dans votre système, vous avez plusieurs options :

#### Option A : Utiliser des préfixes différents

```typescript
// Pour les endpoints REST via OpenAPI
path: "/api/rest/health"

// Pour les endpoints tRPC classiques
// Pas de changement nécessaire
```

#### Option B : Configurer la priorité des handlers

```typescript
// apps/app/src/app/api/[...openapi]/route.ts
const handler = (req: NextRequest) => {
  return createOpenApiFetchHandler({
    router: appRouter,
    endpoint: "/api/rest", // Préfixe spécifique pour OpenAPI
    // ...
  })
}
```

#### Option C : Migrer progressivement

1. Commencez par de nouveaux endpoints
2. Migrez progressivement les endpoints existants
3. Utilisez des redirections temporaires si nécessaire

### Recommandation

**Utilisez l'Option A** avec le préfixe `/api/rest/` pour les endpoints OpenAPI. Cela évite tous les conflits et rend l'architecture claire.

## ✅ Bonnes pratiques

### 1. **Nommage des endpoints**

```typescript
// ✅ Bon : Cohérent et prévisible
GET /api/rest/users          # Liste des utilisateurs
GET /api/rest/users/{id}     # Utilisateur par ID
POST /api/rest/users         # Créer un utilisateur
PUT /api/rest/users/{id}     # Modifier un utilisateur
DELETE /api/rest/users/{id}  # Supprimer un utilisateur

// ❌ Éviter : Incohérent
GET /api/rest/getUserList
GET /api/rest/user-detail/{id}
POST /api/rest/createNewUser
```

### 2. **Organisation des tags**

```typescript
// Grouper logiquement les endpoints
tags: ["Utilisateurs"]     # Pour tous les endpoints users
tags: ["Authentification"] # Pour login, logout, etc.
tags: ["Fichiers"]         # Pour upload, download, etc.
tags: ["Système"]          # Pour health, metrics, etc.
```

### 3. **Descriptions détaillées**

```typescript
.meta({
  openapi: {
    method: "POST",
    path: "/api/rest/users",
    tags: ["Utilisateurs"],
    summary: "Création d'un nouvel utilisateur", // Court et précis
    description: `
      Crée un nouveau compte utilisateur avec les informations fournies.
      
      **Validation :**
      - L'email doit être unique
      - Le mot de passe doit contenir au moins 8 caractères
      - Le nom d'utilisateur est optionnel
      
      **Réponse :**
      - Retourne les informations de l'utilisateur créé
      - Un email de vérification est envoyé automatiquement
    `,
  },
})
```

### 4. **Gestion d'erreurs cohérente**

```typescript
// Utilisez des schémas d'erreur standardisés
const errorSchema = z.object({
  error: z.object({
    code: z.string(),
    message: z.string(),
    details: z.any().optional(),
  }),
  timestamp: z.string(),
})
```

## 🚀 Workflow de développement

1. **Créer la procédure tRPC** avec la logique métier
2. **Ajouter les métadonnées OpenAPI** avec `.meta()`
3. **Tester l'endpoint tRPC** avec le client tRPC
4. **Vérifier la documentation** sur `/fr/apidocs`
5. **Générer le SDK Flutter** avec `npm run generate:flutter-sdk`
6. **Tester l'endpoint REST** avec curl ou Postman

## 📝 Checklist pour un nouvel endpoint

- [ ] Procédure tRPC créée avec logique métier
- [ ] Métadonnées OpenAPI ajoutées avec `.meta()`
- [ ] Schémas Zod avec descriptions détaillées
- [ ] Tags appropriés pour l'organisation
- [ ] Gestion d'erreurs cohérente
- [ ] Tests tRPC fonctionnels
- [ ] Documentation Swagger vérifiée
- [ ] SDK Flutter généré et testé

---

Cette architecture garantit une **maintenance simplifiée** et une **synchronisation automatique** entre vos endpoints tRPC et REST, tout en évitant la duplication de code.
