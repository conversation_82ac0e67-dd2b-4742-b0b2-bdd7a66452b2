#!/usr/bin/env node

/**
 * Script de génération automatique du SDK Flutter
 *
 * Ce script génère automatiquement un SDK Flutter/Dart à partir de la spécification
 * OpenAPI de l'API ProAmLink. Il utilise openapi-generator-cli pour créer un client
 * Dart avec le générateur dio.
 *
 * Fonctionnalités :
 * - Téléchargement automatique de la spécification OpenAPI
 * - Génération du SDK Flutter avec openapi-generator-cli
 * - Placement du SDK dans le répertoire mobile approprié
 * - Configuration automatique du client avec authentification
 * - Nettoyage et organisation des fichiers générés
 *
 * Usage :
 *   npm run generate:flutter-sdk
 *   node packages/scripts/generate-flutter-sdk.js
 */

import { execSync } from "child_process"
import * as fs from "fs"
import * as http from "http"
import * as https from "https"
import * as path from "path"

import { logger } from "@proamlink/lib"

// Configuration
interface Config {
  openApiUrl: string
  projectRoot: string
  mobileDir: string
  sdkOutputDir: string
  tempDir: string
  generator: string
  packageName: string
  packageVersion: string
  clientName: string
}

const CONFIG: Config = {
  // URL de la spécification OpenAPI
  // eslint-disable-next-line no-process-env
  openApiUrl: process.env.OPENAPI_URL || "http://localhost:3000/api/openapi.json",

  // Répertoires
  projectRoot: path.resolve(__dirname, "../.."),
  mobileDir: path.resolve(__dirname, "../../apps/mobile"),
  sdkOutputDir: path.resolve(__dirname, "../../apps/mobile/lib/domain/proamlink"),
  tempDir: path.resolve(__dirname, "../../temp"),

  // Configuration du générateur
  generator: "dart-dio",
  packageName: "proamlink_api",
  packageVersion: "1.0.0",

  // Configuration du client
  clientName: "ProAmLinkApiClient",
}

/**
 * Utilitaires pour les logs
 */
function logStep(step: string, message: string): void {
  logger.log(`[${step}] ${message}`)
}

function logSuccess(message: string): void {
  logger.log(`✓ ${message}`)
}

function logError(message: string): void {
  logger.error(`✗ ${message}`)
}

function logWarning(message: string): void {
  logger.warn(`⚠ ${message}`)
}

/**
 * Télécharge la spécification OpenAPI
 */
async function downloadOpenApiSpec(): Promise<string> {
  logStep("1/6", "Téléchargement de la spécification OpenAPI")

  return new Promise((resolve, reject) => {
    const specPath = path.join(CONFIG.tempDir, "openapi.json")

    // Créer le répertoire temporaire
    if (!fs.existsSync(CONFIG.tempDir)) {
      fs.mkdirSync(CONFIG.tempDir, { recursive: true })
    }

    const file = fs.createWriteStream(specPath)

    // Choisir le bon module selon le protocole
    const isHttps = CONFIG.openApiUrl.startsWith("https:")
    const httpModule = isHttps ? https : http

    httpModule
      .get(CONFIG.openApiUrl, (response) => {
        if (response.statusCode !== 200) {
          reject(new Error(`Erreur HTTP ${response.statusCode} lors du téléchargement de la spécification OpenAPI`))
          return
        }

        response.pipe(file)

        file.on("finish", () => {
          file.close()
          logSuccess(`Spécification téléchargée : ${specPath}`)
          resolve(specPath)
        })
      })
      .on("error", (err: Error) => {
        fs.unlink(specPath, () => {}) // Nettoyer le fichier en cas d'erreur
        reject(err)
      })
  })
}

/**
 * Valide la spécification OpenAPI
 */
function validateOpenApiSpec(specPath: string): unknown {
  logStep("2/6", "Validation de la spécification OpenAPI")

  try {
    const spec = JSON.parse(fs.readFileSync(specPath, "utf8"))

    // Vérifications de base
    if (!spec.openapi) {
      throw new Error('Champ "openapi" manquant dans la spécification')
    }

    if (!spec.info) {
      throw new Error('Champ "info" manquant dans la spécification')
    }

    if (!spec.paths || Object.keys(spec.paths).length === 0) {
      throw new Error("Aucun endpoint trouvé dans la spécification")
    }

    logSuccess(`Spécification valide - ${Object.keys(spec.paths).length} endpoints trouvés`)
    return spec
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error)
    throw new Error(`Erreur de validation : ${errorMessage}`)
  }
}

/**
 * Génère le SDK Flutter avec openapi-generator-cli
 */
function generateFlutterSdk(specPath: string): string {
  logStep("3/6", "Génération du SDK Flutter")

  const tempSdkDir = path.join(CONFIG.tempDir, "flutter-sdk")

  // Nettoyer le répertoire temporaire du SDK
  if (fs.existsSync(tempSdkDir)) {
    fs.rmSync(tempSdkDir, { recursive: true, force: true })
  }

  // Configuration du générateur
  const generatorConfig = {
    packageName: CONFIG.packageName,
    packageVersion: CONFIG.packageVersion,
    clientPackage: CONFIG.clientName,
    pubName: CONFIG.packageName,
    pubVersion: CONFIG.packageVersion,
    pubDescription: "ProAmLink_Flutter_SDK",
    pubAuthor: "ProAmLink_Team",
    pubHomepage: "https://github.com/proamlink/proamlink",
    sourceFolder: "lib",
    // Configuration spécifique à Dart-Dio
    nullSafe: "true",
    nullSafeArrayDefault: "true",
    serializationLibrary: "built_value",
  }

  // Construire la commande openapi-generator-cli avec échappement des valeurs
  const configArgs = Object.entries(generatorConfig)
    .map(([key, value]) => `--additional-properties=${key}="${value}"`)
    .join(" ")

  const command = `npx @openapitools/openapi-generator-cli generate --input-spec "${specPath}" --generator-name ${CONFIG.generator} --output "${tempSdkDir}" --package-name ${CONFIG.packageName} ${configArgs} --skip-validate-spec`

  try {
    logger.log(`Exécution : ${command}`)
    execSync(command, {
      stdio: "inherit",
      cwd: CONFIG.projectRoot,
    })

    logSuccess("SDK Flutter généré avec succès")
    return tempSdkDir
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error)
    throw new Error(`Erreur lors de la génération du SDK : ${errorMessage}`)
  }
}

/**
 * Organise et copie les fichiers du SDK
 */
function organizeSdkFiles(tempSdkDir: string): void {
  logStep("4/6", "Organisation des fichiers du SDK")

  // Nettoyer le répertoire de destination
  if (fs.existsSync(CONFIG.sdkOutputDir)) {
    fs.rmSync(CONFIG.sdkOutputDir, { recursive: true, force: true })
  }

  // Créer le répertoire de destination
  fs.mkdirSync(CONFIG.sdkOutputDir, { recursive: true })

  // Copier les fichiers essentiels
  const filesToCopy = ["lib", "pubspec.yaml", "README.md", "CHANGELOG.md"]

  filesToCopy.forEach((file) => {
    const srcPath = path.join(tempSdkDir, file)
    const destPath = path.join(CONFIG.sdkOutputDir, file)

    if (fs.existsSync(srcPath)) {
      if (fs.statSync(srcPath).isDirectory()) {
        fs.cpSync(srcPath, destPath, { recursive: true })
      } else {
        fs.copyFileSync(srcPath, destPath)
      }
      logSuccess(`Copié : ${file}`)
    } else {
      logWarning(`Fichier non trouvé : ${file}`)
    }
  })
}

/**
 * Crée un fichier de configuration pour le SDK
 */
function createSdkConfig(): void {
  logStep("5/6", "Création de la configuration du SDK")

  const configContent = `
// Configuration automatiquement générée pour le SDK ProAmLink
// Ne pas modifier manuellement - ce fichier est régénéré automatiquement

/// Configuration par défaut pour le client API ProAmLink
class ProAmLinkConfig {
  /// URL de base de l'API (production)
  static const String baseUrlProduction = 'https://api.proamlink.com';

  /// URL de base de l'API (développement)
  static const String baseUrlDevelopment = 'http://localhost:3000';

  /// URL de base de l'API (staging)
  static const String baseUrlStaging = 'https://staging-api.proamlink.com';

  /// Timeout par défaut pour les requêtes (en millisecondes)
  static const int defaultTimeout = 30000;

  /// Version du SDK
  static const String sdkVersion = '${CONFIG.packageVersion}';

  /// Date de génération du SDK
  static const String generatedAt = '${new Date().toISOString()}';

  /// Obtient l'URL de base selon l'environnement
  static String getBaseUrl({bool isProduction = false, bool isStaging = false}) {
    if (isProduction) return baseUrlProduction;
    if (isStaging) return baseUrlStaging;
    return baseUrlDevelopment;
  }
}
`

  const configPath = path.join(CONFIG.sdkOutputDir, "lib", "config.dart")
  fs.writeFileSync(configPath, configContent.trim())

  logSuccess("Configuration du SDK créée")
}

/**
 * Nettoie les fichiers temporaires
 */
function cleanup(): void {
  logStep("6/6", "Nettoyage des fichiers temporaires")

  if (fs.existsSync(CONFIG.tempDir)) {
    fs.rmSync(CONFIG.tempDir, { recursive: true, force: true })
    logSuccess("Fichiers temporaires supprimés")
  }
}

/**
 * Fonction principale
 */
async function main(): Promise<void> {
  await logger.loadChalk()
  try {
    logger.log("Génération du SDK Flutter pour ProAmLink")
    logger.log("Configuration :")
    logger.log(`  - URL OpenAPI : ${CONFIG.openApiUrl}`)
    logger.log(`  - Répertoire de sortie : ${CONFIG.sdkOutputDir}`)
    logger.log(`  - Générateur : ${CONFIG.generator}`)
    logger.log(`  - Package : ${CONFIG.packageName}@${CONFIG.packageVersion}`)

    // Vérifier que le répertoire mobile existe
    if (!fs.existsSync(CONFIG.mobileDir)) {
      throw new Error(`Répertoire mobile non trouvé : ${CONFIG.mobileDir}`)
    }

    // Étapes de génération
    const specPath = await downloadOpenApiSpec()
    validateOpenApiSpec(specPath)
    const tempSdkDir = generateFlutterSdk(specPath)
    organizeSdkFiles(tempSdkDir)
    createSdkConfig()
    cleanup()

    // Succès
    logger.log("✅ SDK Flutter généré avec succès !")
    logger.log(`Répertoire de sortie : ${CONFIG.sdkOutputDir}`)
    logger.log("Prochaines étapes :")
    logger.log("1. Ajouter les dépendances dans pubspec.yaml de l'app mobile")
    logger.log("2. Importer le SDK dans votre code Flutter")
    logger.log("3. Configurer l'authentification Bearer")
    logger.log("4. Tester les endpoints avec le SDK généré")
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error)
    logError(`Erreur lors de la génération du SDK : ${errorMessage}`)

    // Nettoyer en cas d'erreur
    try {
      cleanup()
    } catch (cleanupError) {
      const cleanupErrorMessage = cleanupError instanceof Error ? cleanupError.message : String(cleanupError)
      logWarning(`Erreur lors du nettoyage : ${cleanupErrorMessage}`)
    }

    process.exit(1)
  }
}

// Exécuter le script si appelé directement
if (require.main === module) {
  main().catch((error) => {
    console.error("Erreur fatale:", error)
    process.exit(1)
  })
}

export { CONFIG, main }
